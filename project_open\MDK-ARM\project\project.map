Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.bsp_get_systick) refers to stm32f4xx_hal.o(.data) for uwTick
    main.o(i.delay1_callback) refers to app_motor.o(i.Motor_Init) for Motor_Init
    main.o(i.delay1_callback) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    main.o(i.delay1_callback) refers to main.o(i.delay2_callback) for delay2_callback
    main.o(i.delay1_callback) refers to main.o(.bss) for .bss
    main.o(i.delay2_callback) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    main.o(i.delay2_callback) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    main.o(i.delay2_callback) refers to usart.o(.bss) for huart4
    main.o(i.delay2_callback) refers to main.o(i.delay3_callback) for delay3_callback
    main.o(i.delay2_callback) refers to main.o(.bss) for .bss
    main.o(i.delay3_callback) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    main.o(i.delay3_callback) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    main.o(i.delay3_callback) refers to usart.o(.bss) for huart5
    main.o(i.delay3_callback) refers to main.o(i.delay4_callback) for delay4_callback
    main.o(i.delay3_callback) refers to main.o(.bss) for .bss
    main.o(i.delay4_callback) refers to app_uasrt.o(i.save_initial_position) for save_initial_position
    main.o(i.delay4_callback) refers to app_laser_draw.o(i.app_laser_draw_init) for app_laser_draw_init
    main.o(i.delay4_callback) refers to app_laser_draw.o(i.draw_circle) for draw_circle
    main.o(i.delay4_callback) refers to main.o(.data) for .data
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM8_Init) for MX_TIM8_Init
    main.o(i.main) refers to tim.o(i.MX_TIM12_Init) for MX_TIM12_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to multitimer.o(i.multiTimerInstall) for multiTimerInstall
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to app_trajectory.o(i.app_trajectory_init) for app_trajectory_init
    main.o(i.main) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    main.o(i.main) refers to multitimer.o(i.multiTimerYield) for multiTimerYield
    main.o(i.main) refers to main.o(i.bsp_get_systick) for bsp_get_systick
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_y
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_y
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_x
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_cam
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_cam
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_user
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_hmi
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_hmi
    main.o(i.main) refers to app_botton.o(i.botton_task) for botton_task
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to main.o(i.delay1_callback) for delay1_callback
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM12_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM12_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM12_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM12_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM12_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM12_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM3_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM4_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM8_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM8_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM8_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM8_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(i.my_printf) for my_printf
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(.bss) for ringbuffer_user
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(i.user_task) for user_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_user
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.data) for uart_flag
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(.bss) for ringbuffer_cam
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_maixcam.o(i.maixcam_task) for maixcam_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_cam
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(.bss) for ringbuffer_hmi
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_hmi.o(i.hmi_task) for hmi_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_hmi
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(i.usart_task) for usart_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_usart
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_get) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_init) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_reset) refers to abort.o(.text) for abort
    multitimer.o(i.multiTimerInstall) refers to multitimer.o(.data) for .data
    multitimer.o(i.multiTimerStart) refers to multitimer.o(i.removeTimer) for removeTimer
    multitimer.o(i.multiTimerStart) refers to multitimer.o(.data) for .data
    multitimer.o(i.multiTimerStop) refers to multitimer.o(i.removeTimer) for removeTimer
    multitimer.o(i.multiTimerYield) refers to multitimer.o(.data) for .data
    multitimer.o(i.removeTimer) refers to multitimer.o(.data) for .data
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Parse_Response) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    app_botton.o(i.botton_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_botton.o(i.botton_task) refers to app_botton.o(.data) for .data
    app_botton.o(i.botton_task) refers to main.o(.bss) for mt_botton
    app_hmi.o(i.hmi_parse_data) refers to strlen.o(.text) for strlen
    app_hmi.o(i.hmi_parse_data) refers to app_uasrt.o(i.my_printf) for my_printf
    app_hmi.o(i.hmi_parse_data) refers to app_hmi.o(i.hmi_parse_frame) for hmi_parse_frame
    app_hmi.o(i.hmi_parse_data) refers to app_hmi.o(i.hmi_process_command) for hmi_process_command
    app_hmi.o(i.hmi_parse_data) refers to usart.o(.bss) for huart1
    app_hmi.o(i.hmi_process_command) refers to app_uasrt.o(i.my_printf) for my_printf
    app_hmi.o(i.hmi_process_command) refers to app_trajectory.o(i.app_polyline_start) for app_polyline_start
    app_hmi.o(i.hmi_process_command) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_hmi.o(i.hmi_process_command) refers to app_laser_draw.o(i.start_drawing) for start_drawing
    app_hmi.o(i.hmi_process_command) refers to app_motor.o(i.Motor_Set_Position) for Motor_Set_Position
    app_hmi.o(i.hmi_process_command) refers to app_trajectory.o(i.app_vector_stop) for app_vector_stop
    app_hmi.o(i.hmi_process_command) refers to app_trajectory.o(i.app_vector_reset_to_center) for app_vector_reset_to_center
    app_hmi.o(i.hmi_process_command) refers to app_trajectory.o(i.app_vector_return_and_continue) for app_vector_return_and_continue
    app_hmi.o(i.hmi_process_command) refers to app_hmi.o(.constdata) for .constdata
    app_hmi.o(i.hmi_process_command) refers to usart.o(.bss) for huart1
    app_hmi.o(i.hmi_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_hmi.o(i.hmi_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_hmi.o(i.hmi_task) refers to app_hmi.o(i.hmi_parse_data) for hmi_parse_data
    app_hmi.o(i.hmi_task) refers to rt_memclr.o(.text) for __aeabi_memclr
    app_hmi.o(i.hmi_task) refers to app_uasrt.o(.bss) for ringbuffer_hmi
    app_hmi.o(i.hmi_task) refers to app_uasrt.o(.bss) for output_buffer_hmi
    app_hmi.o(.constdata) refers to app_laser_draw.o(i.sine_function) for sine_function
    app_hmi.o(.constdata) refers to app_laser_draw.o(i.rose_polar_r) for rose_polar_r
    app_maixcam.o(i.default_laser_callback) refers to app_uasrt.o(i.my_printf) for my_printf
    app_maixcam.o(i.default_laser_callback) refers to app_pid.o(i.app_pid_init) for app_pid_init
    app_maixcam.o(i.default_laser_callback) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_maixcam.o(i.default_laser_callback) refers to app_pid.o(i.app_pid_start) for app_pid_start
    app_maixcam.o(i.default_laser_callback) refers to usart.o(.bss) for huart1
    app_maixcam.o(i.maixcam_parse_data) refers to _scanf_int.o(.text) for _scanf_int
    app_maixcam.o(i.maixcam_parse_data) refers to _scanf_str.o(.text) for _scanf_string
    app_maixcam.o(i.maixcam_parse_data) refers to strrchr.o(.text) for strrchr
    app_maixcam.o(i.maixcam_parse_data) refers to __0sscanf.o(.text) for __0sscanf
    app_maixcam.o(i.maixcam_parse_data) refers to app_maixcam.o(.data) for .data
    app_maixcam.o(i.maixcam_set_callback) refers to app_maixcam.o(.data) for .data
    app_maixcam.o(i.maixcam_set_callback) refers to app_maixcam.o(i.default_laser_callback) for default_laser_callback
    app_maixcam.o(i.maixcam_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_maixcam.o(i.maixcam_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_maixcam.o(i.maixcam_task) refers to strncmp.o(.text) for strncmp
    app_maixcam.o(i.maixcam_task) refers to app_maixcam.o(i.maixcam_parse_data) for maixcam_parse_data
    app_maixcam.o(i.maixcam_task) refers to rt_memclr.o(.text) for __aeabi_memclr
    app_maixcam.o(i.maixcam_task) refers to app_maixcam.o(i.process_trajectory_command) for process_trajectory_command
    app_maixcam.o(i.maixcam_task) refers to app_uasrt.o(.bss) for ringbuffer_cam
    app_maixcam.o(i.maixcam_task) refers to app_uasrt.o(.bss) for output_buffer_cam
    app_maixcam.o(i.process_trajectory_command) refers to _scanf_int.o(.text) for _scanf_int
    app_maixcam.o(i.process_trajectory_command) refers to strncmp.o(.text) for strncmp
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_polyline_return_and_continue) for app_polyline_return_and_continue
    app_maixcam.o(i.process_trajectory_command) refers to __0sscanf.o(.text) for __0sscanf
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_pencil_set_rect) for app_pencil_set_rect
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_pencil_start) for app_pencil_start
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_pencil_stop) for app_pencil_stop
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_pencil_reset_to_center) for app_pencil_reset_to_center
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_pencil_return_and_continue) for app_pencil_return_and_continue
    app_maixcam.o(i.process_trajectory_command) refers to app_uasrt.o(i.my_printf) for my_printf
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_vector_set_rect) for app_vector_set_rect
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_vector_start) for app_vector_start
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_vector_stop) for app_vector_stop
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_vector_reset_to_center) for app_vector_reset_to_center
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_vector_return_and_continue) for app_vector_return_and_continue
    app_maixcam.o(i.process_trajectory_command) refers to strchr.o(.text) for strchr
    app_maixcam.o(i.process_trajectory_command) refers to strrchr.o(.text) for strrchr
    app_maixcam.o(i.process_trajectory_command) refers to strtok.o(.text) for strtok
    app_maixcam.o(i.process_trajectory_command) refers to atoi.o(.text) for atoi
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_polyline_set_points) for app_polyline_set_points
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_polyline_start) for app_polyline_start
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_polyline_stop) for app_polyline_stop
    app_maixcam.o(i.process_trajectory_command) refers to app_trajectory.o(i.app_polyline_reset_to_center) for app_polyline_reset_to_center
    app_maixcam.o(i.process_trajectory_command) refers to usart.o(.bss) for huart1
    app_maixcam.o(.data) refers to app_maixcam.o(i.default_laser_callback) for default_laser_callback
    app_motor.o(i.Motor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    app_motor.o(i.Motor_Init) refers to app_motor.o(i.Motor_Stop) for Motor_Stop
    app_motor.o(i.Motor_Init) refers to usart.o(.bss) for huart4
    app_motor.o(i.Motor_Set_Position) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    app_motor.o(i.Motor_Set_Position) refers to usart.o(.bss) for huart4
    app_motor.o(i.Motor_Set_Speed) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    app_motor.o(i.Motor_Set_Speed) refers to usart.o(.bss) for huart4
    app_motor.o(i.Motor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    app_motor.o(i.Motor_Stop) refers to usart.o(.bss) for huart4
    app_oled.o(i.OLED_Clear) refers to app_oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    app_oled.o(i.OLED_Clear) refers to app_oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    app_oled.o(i.OLED_Display_Off) refers to app_oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    app_oled.o(i.OLED_Display_On) refers to app_oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    app_oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_oled.o(i.OLED_Init) refers to app_oled.o(i.WriteCmd) for WriteCmd
    app_oled.o(i.OLED_On) refers to app_oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    app_oled.o(i.OLED_On) refers to app_oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    app_oled.o(i.OLED_SHowfloat) refers to app_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    app_oled.o(i.OLED_SHowfloat) refers to app_oled.o(i.OLED_ShowNum) for OLED_ShowNum
    app_oled.o(i.OLED_Set_Pos) refers to app_oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    app_oled.o(i.OLED_ShowCHinese) refers to app_oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    app_oled.o(i.OLED_ShowCHinese) refers to app_oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    app_oled.o(i.OLED_ShowCHinese) refers to app_oled.o(.constdata) for .constdata
    app_oled.o(i.OLED_ShowChar) refers to app_oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    app_oled.o(i.OLED_ShowChar) refers to app_oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    app_oled.o(i.OLED_ShowChar) refers to app_oled.o(.constdata) for .constdata
    app_oled.o(i.OLED_ShowNum) refers to app_oled.o(i.oled_pow) for oled_pow
    app_oled.o(i.OLED_ShowNum) refers to app_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    app_oled.o(i.OLED_ShowString) refers to app_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    app_oled.o(i.OLED_WR_CMD) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    app_oled.o(i.OLED_WR_CMD) refers to i2c.o(.bss) for hi2c1
    app_oled.o(i.OLED_WR_DATA) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    app_oled.o(i.OLED_WR_DATA) refers to i2c.o(.bss) for hi2c1
    app_oled.o(i.WriteCmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    app_oled.o(i.WriteCmd) refers to app_oled.o(.data) for .data
    app_oled.o(i.WriteCmd) refers to i2c.o(.bss) for hi2c1
    app_oled.o(i.oled_task) refers to app_oled.o(i.OLED_ShowNum) for OLED_ShowNum
    app_oled.o(i.oled_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_oled.o(i.oled_task) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.oled_task) refers to main.o(.bss) for mt_oled
    app_pid.o(i.app_pid_calc) refers to app_motor.o(i.Motor_Stop) for Motor_Stop
    app_pid.o(i.app_pid_calc) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    app_pid.o(i.app_pid_calc) refers to app_pid.o(i.app_pid_limit_integral) for app_pid_limit_integral
    app_pid.o(i.app_pid_calc) refers to app_motor.o(i.Motor_Set_Speed) for Motor_Set_Speed
    app_pid.o(i.app_pid_calc) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_calc) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_init) refers to pid.o(i.pid_init) for pid_init
    app_pid.o(i.app_pid_init) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_init) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_init) refers to app_pid.o(i.app_pid_report_task) for app_pid_report_task
    app_pid.o(i.app_pid_parse_cmd) refers to _scanf_int.o(.text) for _scanf_int
    app_pid.o(i.app_pid_parse_cmd) refers to __0sscanf.o(.text) for __0sscanf
    app_pid.o(i.app_pid_parse_cmd) refers to strncmp.o(.text) for strncmp
    app_pid.o(i.app_pid_parse_cmd) refers to pid.o(i.pid_set_target) for pid_set_target
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.app_pid_start) for app_pid_start
    app_pid.o(i.app_pid_parse_cmd) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.app_pid_set_x_params) for app_pid_set_x_params
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.app_pid_set_y_params) for app_pid_set_y_params
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.app_pid_stop) for app_pid_stop
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_report) refers to app_uasrt.o(i.my_printf) for my_printf
    app_pid.o(i.app_pid_report) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_report) refers to usart.o(.bss) for huart1
    app_pid.o(i.app_pid_report_task) refers to app_pid.o(i.app_pid_report) for app_pid_report
    app_pid.o(i.app_pid_report_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_report_task) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_set_target) refers to pid.o(i.pid_set_target) for pid_set_target
    app_pid.o(i.app_pid_set_target) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_set_target) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_set_x_params) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_pid.o(i.app_pid_set_x_params) refers to pid.o(i.pid_set_params) for pid_set_params
    app_pid.o(i.app_pid_set_x_params) refers to pid.o(i.pid_set_limit) for pid_set_limit
    app_pid.o(i.app_pid_set_x_params) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_set_x_params) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_set_y_params) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_pid.o(i.app_pid_set_y_params) refers to pid.o(i.pid_set_params) for pid_set_params
    app_pid.o(i.app_pid_set_y_params) refers to pid.o(i.pid_set_limit) for pid_set_limit
    app_pid.o(i.app_pid_set_y_params) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_set_y_params) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_start) refers to app_maixcam.o(i.maixcam_set_callback) for maixcam_set_callback
    app_pid.o(i.app_pid_start) refers to pid.o(i.pid_reset) for pid_reset
    app_pid.o(i.app_pid_start) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_start) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_start) refers to app_pid.o(i.pid_laser_coord_callback) for pid_laser_coord_callback
    app_pid.o(i.app_pid_start) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_start) refers to app_pid.o(i.app_pid_task) for app_pid_task
    app_pid.o(i.app_pid_start) refers to app_pid.o(i.app_pid_report_task) for app_pid_report_task
    app_pid.o(i.app_pid_stop) refers to app_motor.o(i.Motor_Stop) for Motor_Stop
    app_pid.o(i.app_pid_stop) refers to multitimer.o(i.multiTimerStop) for multiTimerStop
    app_pid.o(i.app_pid_stop) refers to app_maixcam.o(i.maixcam_set_callback) for maixcam_set_callback
    app_pid.o(i.app_pid_stop) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_stop) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_task) refers to app_pid.o(i.app_pid_calc) for app_pid_calc
    app_pid.o(i.app_pid_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_task) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_task) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_update_position) refers to app_pid.o(.data) for .data
    app_pid.o(i.pid_laser_coord_callback) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_pid.o(i.pid_laser_coord_callback) refers to app_pid.o(i.app_pid_update_position) for app_pid_update_position
    app_trajectory.o(i.app_pencil_calc_next_point) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_reset_to_center) refers to app_trajectory.o(i.app_pencil_calc_next_point) for app_pencil_calc_next_point
    app_trajectory.o(i.app_pencil_reset_to_center) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_reset_to_center) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_pencil_reset_to_center) refers to app_trajectory.o(i.calc_pencil_center) for calc_pencil_center
    app_trajectory.o(i.app_pencil_reset_to_center) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_pencil_reset_to_center) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_reset_to_center) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_reset_to_center) refers to app_trajectory.o(i.app_pencil_task) for app_pencil_task
    app_trajectory.o(i.app_pencil_restart) refers to app_trajectory.o(i.app_pencil_start) for app_pencil_start
    app_trajectory.o(i.app_pencil_restart) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_restart) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_restart) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_return_and_continue) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_return_and_continue) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_pencil_return_and_continue) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_pencil_return_and_continue) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_return_and_continue) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_return_and_continue) refers to app_trajectory.o(i.app_pencil_task) for app_pencil_task
    app_trajectory.o(i.app_pencil_set_rect) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_set_rect) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_set_rect) refers to app_oled.o(.bss) for text01
    app_trajectory.o(i.app_pencil_set_rect) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_start) refers to app_trajectory.o(i.app_pencil_calc_next_point) for app_pencil_calc_next_point
    app_trajectory.o(i.app_pencil_start) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_pencil_start) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_start) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_pencil_start) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_start) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_start) refers to app_trajectory.o(i.app_pencil_task) for app_pencil_task
    app_trajectory.o(i.app_pencil_stop) refers to multitimer.o(i.multiTimerStop) for multiTimerStop
    app_trajectory.o(i.app_pencil_stop) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_stop) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_stop) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_task) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_pencil_task) refers to app_trajectory.o(i.calc_pencil_center) for calc_pencil_center
    app_trajectory.o(i.app_pencil_task) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_pencil_task) refers to app_trajectory.o(i.app_pencil_calc_next_point) for app_pencil_calc_next_point
    app_trajectory.o(i.app_pencil_task) refers to app_trajectory.o(i.app_trajectory_check_arrival) for app_trajectory_check_arrival
    app_trajectory.o(i.app_pencil_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_pencil_task) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_pencil_task) refers to app_trajectory.o(.data) for .data
    app_trajectory.o(i.app_pencil_task) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_pencil_task) refers to app_pid.o(.data) for current_x
    app_trajectory.o(i.app_polyline_calc_next_point) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_reset_to_center) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_reset_to_center) refers to app_trajectory.o(i.app_polyline_calc_next_point) for app_polyline_calc_next_point
    app_trajectory.o(i.app_polyline_reset_to_center) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_polyline_reset_to_center) refers to app_trajectory.o(i.calc_polyline_center) for calc_polyline_center
    app_trajectory.o(i.app_polyline_reset_to_center) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_polyline_reset_to_center) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_reset_to_center) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_reset_to_center) refers to app_trajectory.o(i.app_polyline_task) for app_polyline_task
    app_trajectory.o(i.app_polyline_reset_to_center) refers to main.o(.bss) for mt_polyline_trajectory
    app_trajectory.o(i.app_polyline_restart) refers to app_trajectory.o(i.app_polyline_start) for app_polyline_start
    app_trajectory.o(i.app_polyline_restart) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_restart) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_restart) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_return_and_continue) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_polyline_return_and_continue) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_return_and_continue) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_return_and_continue) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_set_points) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_set_points) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_set_points) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_smooth_target) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_start) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_start) refers to app_trajectory.o(i.app_polyline_calc_next_point) for app_polyline_calc_next_point
    app_trajectory.o(i.app_polyline_start) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_polyline_start) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_polyline_start) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_start) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_start) refers to app_trajectory.o(i.app_polyline_task) for app_polyline_task
    app_trajectory.o(i.app_polyline_start) refers to main.o(.bss) for mt_polyline_trajectory
    app_trajectory.o(i.app_polyline_stop) refers to multitimer.o(i.multiTimerStop) for multiTimerStop
    app_trajectory.o(i.app_polyline_stop) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_stop) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_stop) refers to main.o(.bss) for mt_polyline_trajectory
    app_trajectory.o(i.app_polyline_stop) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(i.calc_polyline_center) for calc_polyline_center
    app_trajectory.o(i.app_polyline_task) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(i.app_polyline_calc_next_point) for app_polyline_calc_next_point
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(i.app_trajectory_check_arrival) for app_trajectory_check_arrival
    app_trajectory.o(i.app_polyline_task) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(i.app_polyline_smooth_target) for app_polyline_smooth_target
    app_trajectory.o(i.app_polyline_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(.data) for .data
    app_trajectory.o(i.app_polyline_task) refers to app_pid.o(.data) for current_x
    app_trajectory.o(i.app_polyline_task) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_polyline_task) refers to app_trajectory.o(.conststring) for .conststring
    app_trajectory.o(i.app_polyline_task) refers to main.o(.bss) for mt_polyline_trajectory
    app_trajectory.o(i.app_trajectory_check_arrival) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_trajectory.o(i.app_trajectory_check_arrival) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    app_trajectory.o(i.app_trajectory_check_arrival) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_trajectory.o(i.app_trajectory_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_trajectory.o(i.app_trajectory_init) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_calc_next_point) refers to app_trajectory.o(i.calc_edge_length) for calc_edge_length
    app_trajectory.o(i.app_vector_calc_next_point) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_reset_to_center) refers to app_trajectory.o(i.app_vector_calc_next_point) for app_vector_calc_next_point
    app_trajectory.o(i.app_vector_reset_to_center) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_reset_to_center) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_vector_reset_to_center) refers to app_trajectory.o(i.calc_vector_center) for calc_vector_center
    app_trajectory.o(i.app_vector_reset_to_center) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_vector_reset_to_center) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_reset_to_center) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_reset_to_center) refers to app_trajectory.o(i.app_vector_task) for app_vector_task
    app_trajectory.o(i.app_vector_restart) refers to app_trajectory.o(i.app_vector_start) for app_vector_start
    app_trajectory.o(i.app_vector_restart) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_restart) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_restart) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_return_and_continue) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_return_and_continue) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_vector_return_and_continue) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_vector_return_and_continue) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_return_and_continue) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_return_and_continue) refers to app_trajectory.o(i.app_vector_task) for app_vector_task
    app_trajectory.o(i.app_vector_set_rect) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_set_rect) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_set_rect) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_start) refers to app_trajectory.o(i.app_vector_calc_next_point) for app_vector_calc_next_point
    app_trajectory.o(i.app_vector_start) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_vector_start) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_start) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_vector_start) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_start) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_start) refers to app_trajectory.o(i.app_vector_task) for app_vector_task
    app_trajectory.o(i.app_vector_stop) refers to multitimer.o(i.multiTimerStop) for multiTimerStop
    app_trajectory.o(i.app_vector_stop) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_stop) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_stop) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_task) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_trajectory.o(i.app_vector_task) refers to app_trajectory.o(i.calc_vector_center) for calc_vector_center
    app_trajectory.o(i.app_vector_task) refers to app_uasrt.o(i.my_printf) for my_printf
    app_trajectory.o(i.app_vector_task) refers to app_trajectory.o(i.app_vector_calc_next_point) for app_vector_calc_next_point
    app_trajectory.o(i.app_vector_task) refers to app_trajectory.o(i.app_trajectory_check_arrival) for app_trajectory_check_arrival
    app_trajectory.o(i.app_vector_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_trajectory.o(i.app_vector_task) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.app_vector_task) refers to app_trajectory.o(.data) for .data
    app_trajectory.o(i.app_vector_task) refers to usart.o(.bss) for huart1
    app_trajectory.o(i.app_vector_task) refers to app_pid.o(.data) for current_x
    app_trajectory.o(i.calc_edge_length) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_trajectory.o(i.calc_edge_length) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    app_trajectory.o(i.calc_edge_length) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_trajectory.o(i.calc_pencil_center) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.calc_polyline_center) refers to app_trajectory.o(.bss) for .bss
    app_trajectory.o(i.calc_vector_center) refers to app_trajectory.o(.bss) for .bss
    app_uasrt.o(i.check_motor_angle_limits) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.check_motor_angle_limits) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    app_uasrt.o(i.check_motor_angle_limits) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.check_motor_angle_limits) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    app_uasrt.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(i.calc_motor_angle) for calc_motor_angle
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(i.calc_relative_angle) for calc_relative_angle
    app_uasrt.o(i.parse_x_motor_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_uasrt.o(i.parse_x_motor_data) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(.conststring) for .conststring
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(i.calc_motor_angle) for calc_motor_angle
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(i.calc_relative_angle) for calc_relative_angle
    app_uasrt.o(i.parse_y_motor_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_uasrt.o(i.parse_y_motor_data) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(.conststring) for .conststring
    app_uasrt.o(i.print_hex_array_x) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.print_hex_array_x) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.print_hex_array_y) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.print_hex_array_y) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.process_command) refers to _scanf_int.o(.text) for _scanf_int
    app_uasrt.o(i.process_command) refers to strncmp.o(.text) for strncmp
    app_uasrt.o(i.process_command) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uasrt.o(i.process_command) refers to app_uasrt.o(i.process_reset_command) for process_reset_command
    app_uasrt.o(i.process_command) refers to __0sscanf.o(.text) for __0sscanf
    app_uasrt.o(i.process_command) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.process_command) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_uasrt.o(i.process_command) refers to usart.o(.bss) for huart2
    app_uasrt.o(i.process_reset_command) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.process_reset_command) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    app_uasrt.o(i.process_reset_command) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.process_reset_command) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.save_initial_position) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    app_uasrt.o(i.save_initial_position) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.save_initial_position) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.save_initial_position) refers to usart.o(.bss) for huart4
    app_uasrt.o(i.usart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_uasrt.o(i.usart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.print_hex_array_x) for print_hex_array_x
    app_uasrt.o(i.usart_task) refers to emm_v5.o(i.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.parse_x_motor_data) for parse_x_motor_data
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.usart_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.print_hex_array_y) for print_hex_array_y
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.parse_y_motor_data) for parse_y_motor_data
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.check_motor_angle_limits) for check_motor_angle_limits
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(.bss) for .bss
    app_uasrt.o(i.usart_task) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.user_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_uasrt.o(i.user_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_uasrt.o(i.user_task) refers to app_uasrt.o(i.process_command) for process_command
    app_uasrt.o(i.user_task) refers to app_pid.o(i.app_pid_parse_cmd) for app_pid_parse_cmd
    app_uasrt.o(i.user_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_uasrt.o(i.user_task) refers to app_uasrt.o(.bss) for .bss
    app_laser_draw.o(i.app_laser_draw_init) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.app_laser_draw_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.app_laser_draw_task) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.app_laser_draw_task) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    app_laser_draw.o(i.app_laser_draw_task) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.app_laser_draw_task) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    app_laser_draw.o(i.app_laser_draw_task) refers to app_laser_draw.o(i.move_to_point) for move_to_point
    app_laser_draw.o(i.app_laser_draw_task) refers to app_laser_draw.o(i.draw_to_point) for draw_to_point
    app_laser_draw.o(i.app_laser_draw_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_laser_draw.o(i.app_laser_draw_task) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.app_laser_draw_task) refers to app_laser_draw.o(.bss) for .bss
    app_laser_draw.o(i.circle_param_x) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.circle_param_x) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.circle_param_x) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.circle_param_y) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.circle_param_y) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    app_laser_draw.o(i.circle_param_y) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.cosine_function) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.cosine_function) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.cosine_function) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.draw_circle) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.draw_circle) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.draw_circle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    app_laser_draw.o(i.draw_circle) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    app_laser_draw.o(i.draw_circle) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.draw_circle) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    app_laser_draw.o(i.draw_circle) refers to app_laser_draw.o(i.move_to_point) for move_to_point
    app_laser_draw.o(i.draw_circle) refers to app_laser_draw.o(i.draw_to_point) for draw_to_point
    app_laser_draw.o(i.draw_circle) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.draw_line) refers to app_laser_draw.o(i.move_to_point) for move_to_point
    app_laser_draw.o(i.draw_line) refers to app_laser_draw.o(i.draw_to_point) for draw_to_point
    app_laser_draw.o(i.draw_line) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.draw_rectangle) refers to app_laser_draw.o(i.move_to_point) for move_to_point
    app_laser_draw.o(i.draw_rectangle) refers to app_laser_draw.o(i.draw_to_point) for draw_to_point
    app_laser_draw.o(i.draw_rectangle) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.draw_to_point) refers to app_laser_draw.o(i.convert_to_pulse) for convert_to_pulse
    app_laser_draw.o(i.draw_to_point) refers to app_motor.o(i.Motor_Set_Position) for Motor_Set_Position
    app_laser_draw.o(i.draw_to_point) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.get_draw_state) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.heart_polar_r) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.heart_polar_r) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.heart_polar_r) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    app_laser_draw.o(i.heart_polar_r) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.laser_off) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.laser_on) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.move_to_point) refers to app_laser_draw.o(i.convert_to_pulse) for convert_to_pulse
    app_laser_draw.o(i.move_to_point) refers to app_motor.o(i.Motor_Set_Position) for Motor_Set_Position
    app_laser_draw.o(i.move_to_point) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.rose_polar_r) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.rose_polar_r) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.rose_polar_r) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.sine_function) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.sine_function) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    app_laser_draw.o(i.sine_function) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.start_drawing) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_laser_draw.o(i.start_drawing) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_laser_draw.o(i.start_drawing) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.start_drawing) refers to app_laser_draw.o(.bss) for .bss
    app_laser_draw.o(i.start_drawing) refers to app_laser_draw.o(i.app_laser_draw_task) for app_laser_draw_task
    app_laser_draw.o(i.stop_drawing) refers to app_laser_draw.o(.data) for .data
    app_laser_draw.o(i.test_first_point) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    app_laser_draw.o(i.test_first_point) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    app_laser_draw.o(i.test_first_point) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    app_laser_draw.o(i.test_first_point) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    app_laser_draw.o(i.test_first_point) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    app_laser_draw.o(i.test_first_point) refers to app_laser_draw.o(i.convert_to_pulse) for convert_to_pulse
    app_laser_draw.o(i.test_first_point) refers to app_laser_draw.o(i.move_to_point) for move_to_point
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    strtok.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    strtok.o(.text) refers to strtok.o(.data) for .data
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (24 bytes).
    Removing main.o(.bss), (24 bytes).
    Removing main.o(.bss), (24 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (64 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (236 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (46 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (86 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (86 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (54 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (156 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (46 bytes).
    Removing pid.o(i.pid_calculate_incremental), (122 bytes).
    Removing app_botton.o(.rev16_text), (4 bytes).
    Removing app_botton.o(.revsh_text), (4 bytes).
    Removing app_botton.o(.rrx_text), (6 bytes).
    Removing app_botton.o(i.key_read), (2 bytes).
    Removing app_botton.o(.bss), (16 bytes).
    Removing app_botton.o(.data), (1 bytes).
    Removing app_hmi.o(.rev16_text), (4 bytes).
    Removing app_hmi.o(.revsh_text), (4 bytes).
    Removing app_hmi.o(.rrx_text), (6 bytes).
    Removing app_maixcam.o(.rev16_text), (4 bytes).
    Removing app_maixcam.o(.revsh_text), (4 bytes).
    Removing app_maixcam.o(.rrx_text), (6 bytes).
    Removing app_motor.o(.rev16_text), (4 bytes).
    Removing app_motor.o(.revsh_text), (4 bytes).
    Removing app_motor.o(.rrx_text), (6 bytes).
    Removing app_oled.o(.rev16_text), (4 bytes).
    Removing app_oled.o(.revsh_text), (4 bytes).
    Removing app_oled.o(.rrx_text), (6 bytes).
    Removing app_oled.o(i.OLED_Clear), (52 bytes).
    Removing app_oled.o(i.OLED_Display_Off), (24 bytes).
    Removing app_oled.o(i.OLED_Display_On), (24 bytes).
    Removing app_oled.o(i.OLED_Init), (16 bytes).
    Removing app_oled.o(i.OLED_On), (52 bytes).
    Removing app_oled.o(i.OLED_SHowfloat), (244 bytes).
    Removing app_oled.o(i.OLED_Set_Pos), (34 bytes).
    Removing app_oled.o(i.OLED_ShowCHinese), (76 bytes).
    Removing app_oled.o(i.OLED_ShowChar), (136 bytes).
    Removing app_oled.o(i.OLED_ShowNum), (106 bytes).
    Removing app_oled.o(i.OLED_ShowString), (54 bytes).
    Removing app_oled.o(i.OLED_WR_CMD), (36 bytes).
    Removing app_oled.o(i.OLED_WR_DATA), (36 bytes).
    Removing app_oled.o(i.WriteCmd), (52 bytes).
    Removing app_oled.o(i.oled_pow), (16 bytes).
    Removing app_oled.o(i.oled_task), (80 bytes).
    Removing app_oled.o(.constdata), (2248 bytes).
    Removing app_oled.o(.data), (27 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.app_pid_parse_target), (8 bytes).
    Removing app_trajectory.o(.rev16_text), (4 bytes).
    Removing app_trajectory.o(.revsh_text), (4 bytes).
    Removing app_trajectory.o(.rrx_text), (6 bytes).
    Removing app_trajectory.o(i.app_pencil_restart), (68 bytes).
    Removing app_trajectory.o(i.app_polyline_restart), (76 bytes).
    Removing app_trajectory.o(i.app_vector_restart), (68 bytes).
    Removing app_uasrt.o(.rev16_text), (4 bytes).
    Removing app_uasrt.o(.revsh_text), (4 bytes).
    Removing app_uasrt.o(.rrx_text), (6 bytes).
    Removing app_laser_draw.o(.rev16_text), (4 bytes).
    Removing app_laser_draw.o(.revsh_text), (4 bytes).
    Removing app_laser_draw.o(.rrx_text), (6 bytes).
    Removing app_laser_draw.o(i.circle_param_x), (32 bytes).
    Removing app_laser_draw.o(i.circle_param_y), (32 bytes).
    Removing app_laser_draw.o(i.convert_to_coordinate), (36 bytes).
    Removing app_laser_draw.o(i.cosine_function), (32 bytes).
    Removing app_laser_draw.o(i.draw_line), (48 bytes).
    Removing app_laser_draw.o(i.draw_rectangle), (96 bytes).
    Removing app_laser_draw.o(i.get_draw_state), (12 bytes).
    Removing app_laser_draw.o(i.heart_polar_r), (52 bytes).
    Removing app_laser_draw.o(i.laser_off), (12 bytes).
    Removing app_laser_draw.o(i.laser_on), (12 bytes).
    Removing app_laser_draw.o(i.line_function), (18 bytes).
    Removing app_laser_draw.o(i.parabola_function), (6 bytes).
    Removing app_laser_draw.o(i.spiral_polar_r), (16 bytes).
    Removing app_laser_draw.o(i.stop_drawing), (16 bytes).
    Removing app_laser_draw.o(i.test_first_point), (238 bytes).

561 unused section(s) (total 47118 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strrchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\App\app_botton.c                      0x00000000   Number         0  app_botton.o ABSOLUTE
    ..\App\app_hmi.c                         0x00000000   Number         0  app_hmi.o ABSOLUTE
    ..\App\app_laser_draw.c                  0x00000000   Number         0  app_laser_draw.o ABSOLUTE
    ..\App\app_maixcam.c                     0x00000000   Number         0  app_maixcam.o ABSOLUTE
    ..\App\app_motor.c                       0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\App\app_oled.c                        0x00000000   Number         0  app_oled.o ABSOLUTE
    ..\App\app_pid.c                         0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\App\app_trajectory.c                  0x00000000   Number         0  app_trajectory.o ABSOLUTE
    ..\App\app_uasrt.c                       0x00000000   Number         0  app_uasrt.o ABSOLUTE
    ..\Components\motor\Emm_V5.c             0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\Components\multi_timer\MultiTimer.c   0x00000000   Number         0  multitimer.o ABSOLUTE
    ..\Components\pid\pid.c                  0x00000000   Number         0  pid.o ABSOLUTE
    ..\Components\ringbuffer\ringbuffer.c    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\App\\app_botton.c                    0x00000000   Number         0  app_botton.o ABSOLUTE
    ..\\App\\app_hmi.c                       0x00000000   Number         0  app_hmi.o ABSOLUTE
    ..\\App\\app_laser_draw.c                0x00000000   Number         0  app_laser_draw.o ABSOLUTE
    ..\\App\\app_maixcam.c                   0x00000000   Number         0  app_maixcam.o ABSOLUTE
    ..\\App\\app_motor.c                     0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\\App\\app_oled.c                      0x00000000   Number         0  app_oled.o ABSOLUTE
    ..\\App\\app_pid.c                       0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\\App\\app_trajectory.c                0x00000000   Number         0  app_trajectory.o ABSOLUTE
    ..\\App\\app_uasrt.c                     0x00000000   Number         0  app_uasrt.o ABSOLUTE
    ..\\Components\\motor\\Emm_V5.c          0x00000000   Number         0  emm_v5.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000314   Section      238  lludivv7m.o(.text)
    .text                                    0x08000404   Section        0  vsnprintf.o(.text)
    .text                                    0x08000438   Section        0  __0sscanf.o(.text)
    .text                                    0x08000474   Section        0  _scanf_int.o(.text)
    .text                                    0x080005c0   Section        0  _scanf_str.o(.text)
    .text                                    0x080006a0   Section        0  atoi.o(.text)
    .text                                    0x080006ba   Section        0  abort.o(.text)
    .text                                    0x080006d0   Section        0  strtok.o(.text)
    .text                                    0x080006dc   Section        0  strchr.o(.text)
    .text                                    0x080006f0   Section        0  strrchr.o(.text)
    .text                                    0x08000706   Section        0  strlen.o(.text)
    .text                                    0x08000744   Section        0  strncmp.o(.text)
    .text                                    0x080007da   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000864   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080008c8   Section       68  rt_memclr.o(.text)
    .text                                    0x0800090c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800095a   Section        0  heapauxi.o(.text)
    .text                                    0x08000960   Section        0  sys_exit.o(.text)
    .text                                    0x0800096c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000974   Section        0  _rserrno.o(.text)
    .text                                    0x0800098a   Section        0  _printf_pad.o(.text)
    .text                                    0x080009d8   Section        0  _printf_truncate.o(.text)
    .text                                    0x080009fc   Section        0  _printf_str.o(.text)
    .text                                    0x08000a50   Section        0  _printf_dec.o(.text)
    .text                                    0x08000ac8   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000af0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000af1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000b20   Section        0  _sputc.o(.text)
    .text                                    0x08000b2a   Section        0  _snputc.o(.text)
    .text                                    0x08000b3c   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000bf8   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000c74   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000c75   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000ce4   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000ce5   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000d78   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000f00   Section        0  _chval.o(.text)
    .text                                    0x08000f1c   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000f1d   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000f48   Section        0  _sgetc.o(.text)
    .text                                    0x08000f88   Section        0  strtol.o(.text)
    .text                                    0x08000ff8   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08001008   Section        0  strtok_int.o(.text)
    .text                                    0x0800104c   Section        8  libspace.o(.text)
    .text                                    0x08001054   Section        2  use_no_semi.o(.text)
    .text                                    0x08001056   Section        0  indicate_semi.o(.text)
    .text                                    0x08001058   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001068   Section      138  lludiv10.o(.text)
    .text                                    0x080010f2   Section        0  isspace.o(.text)
    .text                                    0x08001104   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080011b6   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080011b9   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x080015d4   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080018d0   Section        0  _printf_char.o(.text)
    .text                                    0x080018fc   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001928   Section        0  _scanf.o(.text)
    .text                                    0x08001c9c   Section        0  _strtoul.o(.text)
    .text                                    0x08001d3a   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001d7a   Section        0  defsig_exit.o(.text)
    .text                                    0x08001d84   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x08001db4   Section        0  strcspn.o(.text)
    .text                                    0x08001dd4   Section        0  strspn.o(.text)
    .text                                    0x08001df0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001e3c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001e44   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001ec4   Section        0  bigflt0.o(.text)
    .text                                    0x08001fa8   Section        0  exit.o(.text)
    .text                                    0x08001fba   Section        0  defsig_general.o(.text)
    .text                                    0x08001fec   Section        0  sys_wrch.o(.text)
    .text                                    0x08001ffc   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x0800207c   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080020ba   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002100   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002160   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08002498   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002574   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800259e   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080025c8   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x0800280c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x08002810   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream1_IRQHandler                0x0800281c   Section        0  stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler)
    i.DMA1_Stream2_IRQHandler                0x08002828   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x08002834   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08002840   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x0800284c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x0800284d   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08002874   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08002875   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080028c8   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080028c9   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080028f0   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x080028f2   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Parse_Response                  0x08002928   Section        0  emm_v5.o(i.Emm_V5_Parse_Response)
    i.Emm_V5_Pos_Control                     0x08002b14   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Read_Sys_Params                 0x08002b82   Section        0  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    i.Emm_V5_Reset_CurPos_To_Zero            0x08002c0e   Section        0  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    i.Emm_V5_Stop_Now                        0x08002c3c   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Emm_V5_Vel_Control                     0x08002c6e   Section        0  emm_v5.o(i.Emm_V5_Vel_Control)
    i.Error_Handler                          0x08002cb0   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08002cb4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002d46   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002d6c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002f0c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002fe0   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x08003050   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08003240   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800324c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08003258   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_MspInit                        0x080033e0   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x0800348c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800349c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080034d0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003510   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003540   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800355c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800359c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080035c0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080036f4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003714   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003734   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003794   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08003b00   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003b28   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003b7c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003c0c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003c68   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08003c90   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08003d6c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003e10   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_MspPostInit                    0x08003eb8   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08003f9c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08004068   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x080040c4   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004120   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x0800416c   Section        0  usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x080042e4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080042e8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004568   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080045cc   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x080048c0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080048c2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x080048c4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004964   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004966   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_DMA_Init                            0x08004968   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x080049e4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08004adc   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x08004b1c   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM12_Init                          0x08004b5c   Section        0  tim.o(i.MX_TIM12_Init)
    i.MX_TIM1_Init                           0x08004bb4   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08004c20   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08004c84   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08004cf4   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_TIM8_Init                           0x08004d64   Section        0  tim.o(i.MX_TIM8_Init)
    i.MX_UART4_Init                          0x08004e2c   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x08004e7c   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08004ecc   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004f1c   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004f6c   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08004fbc   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Init                             0x08004fc0   Section        0  app_motor.o(i.Motor_Init)
    i.Motor_Set_Position                     0x08004fec   Section        0  app_motor.o(i.Motor_Set_Position)
    i.Motor_Set_Speed                        0x08005040   Section        0  app_motor.o(i.Motor_Set_Speed)
    i.Motor_Stop                             0x080050c0   Section        0  app_motor.o(i.Motor_Stop)
    i.NMI_Handler                            0x080050e4   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080050e6   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080050e8   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080050ea   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080050f0   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005184   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08005194   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08005264   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08005278   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08005279   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08005288   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08005289   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080052e8   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08005354   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08005355   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080053bc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080053bd   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x0800540c   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x0800540d   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x0800542e   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x0800542f   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART4_IRQHandler                       0x08005454   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x08005480   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x080054ac   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080054ad   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080054ba   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080054bb   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08005504   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005505   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x0800558a   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x0800558b   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x080055a8   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080055a9   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x080055f6   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080055f7   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005612   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005613   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080056d4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080056d5   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x080057e0   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x08005880   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08005881   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080058f4   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08005920   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x0800594c   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08005978   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x0800597a   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x080059aa   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080059ab   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_cos                           0x080059d0   Section        0  cos.o(i.__hardfp_cos)
    i.__hardfp_sin                           0x08005a98   Section        0  sin.o(i.__hardfp_sin)
    i.__hardfp_sqrt                          0x08005b60   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__ieee754_rem_pio2                     0x08005be0   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x08006018   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x08006188   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x08006280   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x080063b0   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x080063c8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x080063e8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x08006408   Section        0  __printf_wp.o(i._is_digit)
    i.app_laser_draw_init                    0x08006418   Section        0  app_laser_draw.o(i.app_laser_draw_init)
    i.app_laser_draw_task                    0x08006428   Section        0  app_laser_draw.o(i.app_laser_draw_task)
    i.app_pencil_calc_next_point             0x08006594   Section        0  app_trajectory.o(i.app_pencil_calc_next_point)
    i.app_pencil_reset_to_center             0x08006604   Section        0  app_trajectory.o(i.app_pencil_reset_to_center)
    i.app_pencil_return_and_continue         0x080066f4   Section        0  app_trajectory.o(i.app_pencil_return_and_continue)
    i.app_pencil_set_rect                    0x080067ec   Section        0  app_trajectory.o(i.app_pencil_set_rect)
    i.app_pencil_start                       0x0800685c   Section        0  app_trajectory.o(i.app_pencil_start)
    i.app_pencil_stop                        0x08006920   Section        0  app_trajectory.o(i.app_pencil_stop)
    i.app_pencil_task                        0x0800696c   Section        0  app_trajectory.o(i.app_pencil_task)
    i.app_pid_calc                           0x08006c00   Section        0  app_pid.o(i.app_pid_calc)
    i.app_pid_init                           0x08006d14   Section        0  app_pid.o(i.app_pid_init)
    i.app_pid_limit_integral                 0x08006d70   Section        0  app_pid.o(i.app_pid_limit_integral)
    app_pid_limit_integral                   0x08006d71   Thumb Code    36  app_pid.o(i.app_pid_limit_integral)
    i.app_pid_parse_cmd                      0x08006d94   Section        0  app_pid.o(i.app_pid_parse_cmd)
    i.app_pid_report                         0x08006f68   Section        0  app_pid.o(i.app_pid_report)
    i.app_pid_report_task                    0x08006fd4   Section        0  app_pid.o(i.app_pid_report_task)
    i.app_pid_set_target                     0x08006ff4   Section        0  app_pid.o(i.app_pid_set_target)
    i.app_pid_set_x_params                   0x08007028   Section        0  app_pid.o(i.app_pid_set_x_params)
    i.app_pid_set_y_params                   0x0800705c   Section        0  app_pid.o(i.app_pid_set_y_params)
    i.app_pid_start                          0x08007090   Section        0  app_pid.o(i.app_pid_start)
    i.app_pid_stop                           0x08007108   Section        0  app_pid.o(i.app_pid_stop)
    i.app_pid_task                           0x0800713c   Section        0  app_pid.o(i.app_pid_task)
    i.app_pid_update_position                0x08007184   Section        0  app_pid.o(i.app_pid_update_position)
    i.app_polyline_calc_next_point           0x080071a0   Section        0  app_trajectory.o(i.app_polyline_calc_next_point)
    i.app_polyline_reset_to_center           0x0800722c   Section        0  app_trajectory.o(i.app_polyline_reset_to_center)
    i.app_polyline_return_and_continue       0x0800730c   Section        0  app_trajectory.o(i.app_polyline_return_and_continue)
    i.app_polyline_set_points                0x080073bc   Section        0  app_trajectory.o(i.app_polyline_set_points)
    i.app_polyline_smooth_target             0x08007480   Section        0  app_trajectory.o(i.app_polyline_smooth_target)
    i.app_polyline_start                     0x080074f8   Section        0  app_trajectory.o(i.app_polyline_start)
    i.app_polyline_stop                      0x08007600   Section        0  app_trajectory.o(i.app_polyline_stop)
    i.app_polyline_task                      0x08007654   Section        0  app_trajectory.o(i.app_polyline_task)
    i.app_trajectory_check_arrival           0x08007908   Section        0  app_trajectory.o(i.app_trajectory_check_arrival)
    i.app_trajectory_init                    0x08007950   Section        0  app_trajectory.o(i.app_trajectory_init)
    i.app_vector_calc_next_point             0x080079c4   Section        0  app_trajectory.o(i.app_vector_calc_next_point)
    i.app_vector_reset_to_center             0x08007a68   Section        0  app_trajectory.o(i.app_vector_reset_to_center)
    i.app_vector_return_and_continue         0x08007b5c   Section        0  app_trajectory.o(i.app_vector_return_and_continue)
    i.app_vector_set_rect                    0x08007c68   Section        0  app_trajectory.o(i.app_vector_set_rect)
    i.app_vector_start                       0x08007cd4   Section        0  app_trajectory.o(i.app_vector_start)
    i.app_vector_stop                        0x08007d98   Section        0  app_trajectory.o(i.app_vector_stop)
    i.app_vector_task                        0x08007de4   Section        0  app_trajectory.o(i.app_vector_task)
    i.botton_task                            0x08008078   Section        0  app_botton.o(i.botton_task)
    i.bsp_get_systick                        0x080080ac   Section        0  main.o(i.bsp_get_systick)
    i.calc_edge_length                       0x080080b8   Section        0  app_trajectory.o(i.calc_edge_length)
    i.calc_motor_angle                       0x080080f4   Section        0  app_uasrt.o(i.calc_motor_angle)
    i.calc_pencil_center                     0x08008120   Section        0  app_trajectory.o(i.calc_pencil_center)
    i.calc_polyline_center                   0x0800815c   Section        0  app_trajectory.o(i.calc_polyline_center)
    i.calc_relative_angle                    0x080081a0   Section        0  app_uasrt.o(i.calc_relative_angle)
    i.calc_vector_center                     0x080081e8   Section        0  app_trajectory.o(i.calc_vector_center)
    i.check_motor_angle_limits               0x08008224   Section        0  app_uasrt.o(i.check_motor_angle_limits)
    i.convert_to_pulse                       0x08008310   Section        0  app_laser_draw.o(i.convert_to_pulse)
    i.default_laser_callback                 0x08008334   Section        0  app_maixcam.o(i.default_laser_callback)
    default_laser_callback                   0x08008335   Thumb Code    60  app_maixcam.o(i.default_laser_callback)
    i.delay1_callback                        0x08008394   Section        0  main.o(i.delay1_callback)
    i.delay2_callback                        0x080083b8   Section        0  main.o(i.delay2_callback)
    i.delay3_callback                        0x080083e4   Section        0  main.o(i.delay3_callback)
    i.delay4_callback                        0x08008410   Section        0  main.o(i.delay4_callback)
    i.draw_circle                            0x0800843c   Section        0  app_laser_draw.o(i.draw_circle)
    i.draw_to_point                          0x0800852c   Section        0  app_laser_draw.o(i.draw_to_point)
    i.fabs                                   0x08008548   Section        0  fabs.o(i.fabs)
    i.hmi_parse_data                         0x08008560   Section        0  app_hmi.o(i.hmi_parse_data)
    i.hmi_parse_frame                        0x08008608   Section        0  app_hmi.o(i.hmi_parse_frame)
    i.hmi_process_command                    0x0800863c   Section        0  app_hmi.o(i.hmi_process_command)
    i.hmi_task                               0x080087e8   Section        0  app_hmi.o(i.hmi_task)
    i.main                                   0x08008824   Section        0  main.o(i.main)
    i.maixcam_parse_data                     0x0800890c   Section        0  app_maixcam.o(i.maixcam_parse_data)
    i.maixcam_set_callback                   0x080089c0   Section        0  app_maixcam.o(i.maixcam_set_callback)
    i.maixcam_task                           0x080089d4   Section        0  app_maixcam.o(i.maixcam_task)
    i.move_to_point                          0x08008a54   Section        0  app_laser_draw.o(i.move_to_point)
    i.multiTimerInstall                      0x08008a70   Section        0  multitimer.o(i.multiTimerInstall)
    i.multiTimerStart                        0x08008a84   Section        0  multitimer.o(i.multiTimerStart)
    i.multiTimerStop                         0x08008adc   Section        0  multitimer.o(i.multiTimerStop)
    i.multiTimerYield                        0x08008ae8   Section        0  multitimer.o(i.multiTimerYield)
    i.my_printf                              0x08008b28   Section        0  app_uasrt.o(i.my_printf)
    i.parse_x_motor_data                     0x08008b5c   Section        0  app_uasrt.o(i.parse_x_motor_data)
    i.parse_y_motor_data                     0x08008e44   Section        0  app_uasrt.o(i.parse_y_motor_data)
    i.pid_calculate_positional               0x0800912c   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_init                               0x08009194   Section        0  pid.o(i.pid_init)
    i.pid_laser_coord_callback               0x080091c4   Section        0  app_pid.o(i.pid_laser_coord_callback)
    i.pid_out_limit                          0x080091e0   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x080091e1   Thumb Code    38  pid.o(i.pid_out_limit)
    i.pid_reset                              0x08009208   Section        0  pid.o(i.pid_reset)
    i.pid_set_limit                          0x08009230   Section        0  pid.o(i.pid_set_limit)
    i.pid_set_params                         0x08009236   Section        0  pid.o(i.pid_set_params)
    i.pid_set_target                         0x0800923c   Section        0  pid.o(i.pid_set_target)
    i.print_hex_array_x                      0x08009244   Section        0  app_uasrt.o(i.print_hex_array_x)
    i.print_hex_array_y                      0x08009290   Section        0  app_uasrt.o(i.print_hex_array_y)
    i.process_command                        0x080092dc   Section        0  app_uasrt.o(i.process_command)
    i.process_reset_command                  0x080093f8   Section        0  app_uasrt.o(i.process_reset_command)
    i.process_trajectory_command             0x08009498   Section        0  app_maixcam.o(i.process_trajectory_command)
    i.removeTimer                            0x080099ac   Section        0  multitimer.o(i.removeTimer)
    removeTimer                              0x080099ad   Thumb Code    24  multitimer.o(i.removeTimer)
    i.rose_polar_r                           0x080099c8   Section        0  app_laser_draw.o(i.rose_polar_r)
    i.rt_ringbuffer_data_len                 0x080099f0   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08009a20   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08009a94   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08009ac4   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08009b3c   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x08009b3d   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    i.save_initial_position                  0x08009b5c   Section        0  app_uasrt.o(i.save_initial_position)
    i.sine_function                          0x08009bb0   Section        0  app_laser_draw.o(i.sine_function)
    i.start_drawing                          0x08009bd0   Section        0  app_laser_draw.o(i.start_drawing)
    i.usart_task                             0x08009c5c   Section        0  app_uasrt.o(i.usart_task)
    i.user_task                              0x08009d34   Section        0  app_uasrt.o(i.user_task)
    locale$$code                             0x08009d7c   Section       44  lc_ctype_c.o(locale$$code)
    locale$$code                             0x08009da8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$basic                              0x08009dd4   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08009dd4   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x08009dec   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08009dec   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08009e50   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08009e50   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08009e61   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$ddiv                               0x08009fa0   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08009fa0   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08009fa7   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x0800a250   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x0800a250   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x0800a2ae   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x0800a2ae   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x0800a2dc   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x0800a2dc   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x0800a304   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800a304   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800a458   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800a458   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800a4f4   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800a4f4   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x0800a500   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800a500   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800a518   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0800a518   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800a6b0   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800a6b0   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800a6c1   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800a884   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800a884   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800a8da   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800a8da   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800a966   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800a966   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800a970   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800a970   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800a97a   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800a97a   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0800a97e   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0800a97e   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x0800a982   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x0800a982   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x0800a982   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800a98a   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800a99a   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800a9a4   Section      112  app_hmi.o(.constdata)
    .constdata                               0x0800aa14   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0800aa14   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800aa1c   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800aa1c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800aa30   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800aa44   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800aa44   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800aa58   Section       48  cos_i.o(.constdata)
    C                                        0x0800aa58   Data          48  cos_i.o(.constdata)
    .constdata                               0x0800aa88   Section      200  rred.o(.constdata)
    pio2s                                    0x0800aa88   Data          48  rred.o(.constdata)
    twooverpi                                0x0800aab8   Data         152  rred.o(.constdata)
    .constdata                               0x0800ab50   Section       40  sin_i.o(.constdata)
    S                                        0x0800ab50   Data          40  sin_i.o(.constdata)
    .constdata                               0x0800ab78   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800ab78   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800ab8b   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800aba0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800aba0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800abdc   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800ac34   Section       76  app_trajectory.o(.conststring)
    .conststring                             0x0800ac80   Section      135  app_uasrt.o(.conststring)
    locale$$data                             0x0800ad28   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x0800ad2c   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800ad34   Data           0  lc_ctype_c.o(locale$$data)
    locale$$data                             0x0800ae38   Section       28  lc_numeric_c.o(locale$$data)
    __lcctype_c_end                          0x0800ae38   Data           0  lc_ctype_c.o(locale$$data)
    __lcnum_c_name                           0x0800ae3c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800ae44   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800ae50   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800ae52   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800ae53   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x0800ae54   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        1  main.o(.data)
    .data                                    0x20000004   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section        8  multitimer.o(.data)
    timerList                                0x20000014   Data           4  multitimer.o(.data)
    platformTicksFunction                    0x20000018   Data           4  multitimer.o(.data)
    .data                                    0x2000001c   Section        4  app_botton.o(.data)
    .data                                    0x20000020   Section        4  app_maixcam.o(.data)
    laser_coord_callback                     0x20000020   Data           4  app_maixcam.o(.data)
    .data                                    0x20000024   Section       92  app_pid.o(.data)
    pid_running                              0x20000024   Data           1  app_pid.o(.data)
    .data                                    0x20000080   Section        9  app_trajectory.o(.data)
    start_wait_count                         0x20000080   Data           1  app_trajectory.o(.data)
    reset_wait_count                         0x20000081   Data           1  app_trajectory.o(.data)
    return_wait_count                        0x20000082   Data           1  app_trajectory.o(.data)
    start_wait_count                         0x20000083   Data           1  app_trajectory.o(.data)
    reset_wait_count                         0x20000084   Data           1  app_trajectory.o(.data)
    return_wait_count                        0x20000085   Data           1  app_trajectory.o(.data)
    reset_wait_count                         0x20000086   Data           1  app_trajectory.o(.data)
    return_wait_count                        0x20000087   Data           1  app_trajectory.o(.data)
    start_wait_count                         0x20000088   Data           1  app_trajectory.o(.data)
    .data                                    0x2000008c   Section       40  app_uasrt.o(.data)
    .data                                    0x200000b4   Section       12  app_laser_draw.o(.data)
    draw_state                               0x200000b4   Data           1  app_laser_draw.o(.data)
    laser_state                              0x200000b5   Data           1  app_laser_draw.o(.data)
    first_point                              0x200000b6   Data           1  app_laser_draw.o(.data)
    draw_loop_enabled                        0x200000b7   Data           1  app_laser_draw.o(.data)
    current_progress                         0x200000b8   Data           4  app_laser_draw.o(.data)
    draw_count                               0x200000bc   Data           4  app_laser_draw.o(.data)
    .data                                    0x200000c0   Section        4  strtok.o(.data)
    _strtok_saves1                           0x200000c0   Data           4  strtok.o(.data)
    .bss                                     0x200000c8   Section       24  main.o(.bss)
    .bss                                     0x200000e0   Section       24  main.o(.bss)
    .bss                                     0x200000f8   Section       24  main.o(.bss)
    .bss                                     0x20000110   Section      120  main.o(.bss)
    .bss                                     0x20000188   Section       24  main.o(.bss)
    .bss                                     0x200001a0   Section       24  main.o(.bss)
    .bss                                     0x200001b8   Section      168  i2c.o(.bss)
    .bss                                     0x20000260   Section      432  tim.o(.bss)
    .bss                                     0x20000410   Section     1480  usart.o(.bss)
    .bss                                     0x200009d8   Section       40  app_oled.o(.bss)
    .bss                                     0x20000a00   Section      168  app_pid.o(.bss)
    mt_pid                                   0x20000a78   Data          24  app_pid.o(.bss)
    mt_pid_report                            0x20000a90   Data          24  app_pid.o(.bss)
    .bss                                     0x20000aa8   Section      600  app_trajectory.o(.bss)
    .bss                                     0x20000d00   Section      128  app_uasrt.o(.bss)
    .bss                                     0x20000d80   Section      128  app_uasrt.o(.bss)
    .bss                                     0x20000e00   Section      128  app_uasrt.o(.bss)
    .bss                                     0x20000e80   Section      128  app_uasrt.o(.bss)
    .bss                                     0x20000f00   Section      128  app_uasrt.o(.bss)
    .bss                                     0x20000f80   Section      420  app_uasrt.o(.bss)
    .bss                                     0x20001124   Section      128  app_uasrt.o(.bss)
    .bss                                     0x200011a4   Section      128  app_uasrt.o(.bss)
    .bss                                     0x20001224   Section       12  app_uasrt.o(.bss)
    .bss                                     0x20001230   Section       12  app_uasrt.o(.bss)
    .bss                                     0x20001240   Section       80  app_laser_draw.o(.bss)
    current_function                         0x20001240   Data          16  app_laser_draw.o(.bss)
    current_params                           0x20001250   Data          40  app_laser_draw.o(.bss)
    mt_laser_draw                            0x20001278   Data          24  app_laser_draw.o(.bss)
    .bss                                     0x20001290   Section       96  libspace.o(.bss)
    HEAP                                     0x200012f0   Section     2048  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x200012f0   Data        2048  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20001af0   Section     3840  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20001af0   Data        3840  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x200029f0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000315   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000315   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000405   Thumb Code    48  vsnprintf.o(.text)
    __0sscanf                                0x08000439   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08000475   Thumb Code   332  _scanf_int.o(.text)
    _scanf_string                            0x080005c1   Thumb Code   224  _scanf_str.o(.text)
    atoi                                     0x080006a1   Thumb Code    26  atoi.o(.text)
    abort                                    0x080006bb   Thumb Code    22  abort.o(.text)
    strtok                                   0x080006d1   Thumb Code     6  strtok.o(.text)
    strchr                                   0x080006dd   Thumb Code    20  strchr.o(.text)
    strrchr                                  0x080006f1   Thumb Code    22  strrchr.o(.text)
    strlen                                   0x08000707   Thumb Code    62  strlen.o(.text)
    strncmp                                  0x08000745   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x080007db   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080007db   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000841   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08000865   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000865   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000865   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080008ad   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x080008c9   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080008c9   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080008cd   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800090d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800090d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800090d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000911   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800095b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800095d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800095f   Thumb Code     2  heapauxi.o(.text)
    _sys_exit                                0x08000961   Thumb Code     8  sys_exit.o(.text)
    __aeabi_errno_addr                       0x0800096d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800096d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800096d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x08000975   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800097f   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x0800098b   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080009b7   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080009d9   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080009eb   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x080009fd   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000a51   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000ac9   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000afb   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000b21   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000b2b   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000b3d   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000bf9   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000c75   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000cb7   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000ccf   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000ce5   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000d3b   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000d57   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000d63   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000d79   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _chval                                   0x08000f01   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000f29   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000f49   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000f67   Thumb Code    34  _sgetc.o(.text)
    strtol                                   0x08000f89   Thumb Code   112  strtol.o(.text)
    __rt_SIGABRT                             0x08000ff9   Thumb Code    14  defsig_abrt_outer.o(.text)
    __strtok_internal                        0x08001009   Thumb Code    64  strtok_int.o(.text)
    __user_libspace                          0x0800104d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800104d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800104d   Thumb Code     0  libspace.o(.text)
    __I$use$semihosting                      0x08001055   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001055   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001057   Thumb Code     0  indicate_semi.o(.text)
    __rt_ctype_table                         0x08001059   Thumb Code    16  rt_ctype_table.o(.text)
    _ll_udiv10                               0x08001069   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x080010f3   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08001105   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x080011b7   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08001369   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x080015d5   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x080018d1   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080018e5   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080018f5   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x080018fd   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001911   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001921   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x08001929   Thumb Code   880  _scanf.o(.text)
    _strtoul                                 0x08001c9d   Thumb Code   158  _strtoul.o(.text)
    _wcrtomb                                 0x08001d3b   Thumb Code    64  _wcrtomb.o(.text)
    __sig_exit                               0x08001d7b   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001d85   Thumb Code    14  defsig_abrt_inner.o(.text)
    strcspn                                  0x08001db5   Thumb Code    32  strcspn.o(.text)
    strspn                                   0x08001dd5   Thumb Code    28  strspn.o(.text)
    __user_setup_stackheap                   0x08001df1   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_locale                              0x08001e3d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001e45   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001ec5   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001fa9   Thumb Code    18  exit.o(.text)
    __default_signal_display                 0x08001fbb   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x08001fed   Thumb Code    14  sys_wrch.o(.text)
    strcmp                                   0x08001ffd   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x0800207d   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080020bb   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002101   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002161   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08002499   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002575   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800259f   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080025c9   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x0800280d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x08002811   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream1_IRQHandler                  0x0800281d   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler)
    DMA1_Stream2_IRQHandler                  0x08002829   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x08002835   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08002841   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x080028f1   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x080028f3   Thumb Code    54  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Parse_Response                    0x08002929   Thumb Code   492  emm_v5.o(i.Emm_V5_Parse_Response)
    Emm_V5_Pos_Control                       0x08002b15   Thumb Code   110  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Read_Sys_Params                   0x08002b83   Thumb Code   140  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    Emm_V5_Reset_CurPos_To_Zero              0x08002c0f   Thumb Code    46  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    Emm_V5_Stop_Now                          0x08002c3d   Thumb Code    50  emm_v5.o(i.Emm_V5_Stop_Now)
    Emm_V5_Vel_Control                       0x08002c6f   Thumb Code    66  emm_v5.o(i.Emm_V5_Vel_Control)
    Error_Handler                            0x08002cb1   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08002cb5   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002d47   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002d6d   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002f0d   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002fe1   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08003051   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08003241   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800324d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08003259   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x080033e1   Thumb Code   154  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800348d   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800349d   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080034d1   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003511   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003541   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800355d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800359d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080035c1   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080036f5   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003715   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003735   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003795   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003b01   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003b29   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003b7d   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003c0d   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003c69   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08003c91   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003d6d   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003e11   Thumb Code   152  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_MspPostInit                      0x08003eb9   Thumb Code   190  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08003f9d   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08004069   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x080040c5   Thumb Code    74  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004121   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x0800416d   Thumb Code   266  usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x080042e5   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080042e9   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004569   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080045cd   Thumb Code   704  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x080048c1   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080048c3   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080048c5   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004965   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004967   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_DMA_Init                              0x08004969   Thumb Code   120  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x080049e5   Thumb Code   230  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08004add   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x08004b1d   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_TIM12_Init                            0x08004b5d   Thumb Code    80  tim.o(i.MX_TIM12_Init)
    MX_TIM1_Init                             0x08004bb5   Thumb Code    98  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08004c21   Thumb Code    96  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08004c85   Thumb Code   102  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004cf5   Thumb Code   102  tim.o(i.MX_TIM4_Init)
    MX_TIM8_Init                             0x08004d65   Thumb Code   190  tim.o(i.MX_TIM8_Init)
    MX_UART4_Init                            0x08004e2d   Thumb Code    64  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x08004e7d   Thumb Code    64  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08004ecd   Thumb Code    64  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004f1d   Thumb Code    64  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004f6d   Thumb Code    64  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08004fbd   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Init                               0x08004fc1   Thumb Code    34  app_motor.o(i.Motor_Init)
    Motor_Set_Position                       0x08004fed   Thumb Code    76  app_motor.o(i.Motor_Set_Position)
    Motor_Set_Speed                          0x08005041   Thumb Code   120  app_motor.o(i.Motor_Set_Speed)
    Motor_Stop                               0x080050c1   Thumb Code    26  app_motor.o(i.Motor_Stop)
    NMI_Handler                              0x080050e5   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080050e7   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080050e9   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080050eb   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080050f1   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x08005185   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08005195   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08005265   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x080052e9   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART4_IRQHandler                         0x08005455   Thumb Code    32  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x08005481   Thumb Code    32  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x080057e1   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x080058f5   Thumb Code    32  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08005921   Thumb Code    32  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x0800594d   Thumb Code    32  stm32f4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08005979   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x0800597b   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_cos                             0x080059d1   Thumb Code   180  cos.o(i.__hardfp_cos)
    __hardfp_sin                             0x08005a99   Thumb Code   180  sin.o(i.__hardfp_sin)
    __hardfp_sqrt                            0x08005b61   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __ieee754_rem_pio2                       0x08005be1   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x08006019   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x08006189   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x08006281   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x080063b1   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x080063c9   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x080063e9   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x08006409   Thumb Code    14  __printf_wp.o(i._is_digit)
    app_laser_draw_init                      0x08006419   Thumb Code    10  app_laser_draw.o(i.app_laser_draw_init)
    app_laser_draw_task                      0x08006429   Thumb Code   354  app_laser_draw.o(i.app_laser_draw_task)
    app_pencil_calc_next_point               0x08006595   Thumb Code   108  app_trajectory.o(i.app_pencil_calc_next_point)
    app_pencil_reset_to_center               0x08006605   Thumb Code   142  app_trajectory.o(i.app_pencil_reset_to_center)
    app_pencil_return_and_continue           0x080066f5   Thumb Code   114  app_trajectory.o(i.app_pencil_return_and_continue)
    app_pencil_set_rect                      0x080067ed   Thumb Code    42  app_trajectory.o(i.app_pencil_set_rect)
    app_pencil_start                         0x0800685d   Thumb Code    96  app_trajectory.o(i.app_pencil_start)
    app_pencil_stop                          0x08006921   Thumb Code    36  app_trajectory.o(i.app_pencil_stop)
    app_pencil_task                          0x0800696d   Thumb Code   282  app_trajectory.o(i.app_pencil_task)
    app_pid_calc                             0x08006c01   Thumb Code   268  app_pid.o(i.app_pid_calc)
    app_pid_init                             0x08006d15   Thumb Code    80  app_pid.o(i.app_pid_init)
    app_pid_parse_cmd                        0x08006d95   Thumb Code   392  app_pid.o(i.app_pid_parse_cmd)
    app_pid_report                           0x08006f69   Thumb Code    62  app_pid.o(i.app_pid_report)
    app_pid_report_task                      0x08006fd5   Thumb Code    28  app_pid.o(i.app_pid_report_task)
    app_pid_set_target                       0x08006ff5   Thumb Code    42  app_pid.o(i.app_pid_set_target)
    app_pid_set_x_params                     0x08007029   Thumb Code    42  app_pid.o(i.app_pid_set_x_params)
    app_pid_set_y_params                     0x0800705d   Thumb Code    42  app_pid.o(i.app_pid_set_y_params)
    app_pid_start                            0x08007091   Thumb Code    96  app_pid.o(i.app_pid_start)
    app_pid_stop                             0x08007109   Thumb Code    42  app_pid.o(i.app_pid_stop)
    app_pid_task                             0x0800713d   Thumb Code    58  app_pid.o(i.app_pid_task)
    app_pid_update_position                  0x08007185   Thumb Code    24  app_pid.o(i.app_pid_update_position)
    app_polyline_calc_next_point             0x080071a1   Thumb Code   134  app_trajectory.o(i.app_polyline_calc_next_point)
    app_polyline_reset_to_center             0x0800722d   Thumb Code   122  app_trajectory.o(i.app_polyline_reset_to_center)
    app_polyline_return_and_continue         0x0800730d   Thumb Code    84  app_trajectory.o(i.app_polyline_return_and_continue)
    app_polyline_set_points                  0x080073bd   Thumb Code   112  app_trajectory.o(i.app_polyline_set_points)
    app_polyline_smooth_target               0x08007481   Thumb Code   108  app_trajectory.o(i.app_polyline_smooth_target)
    app_polyline_start                       0x080074f9   Thumb Code   120  app_trajectory.o(i.app_polyline_start)
    app_polyline_stop                        0x08007601   Thumb Code    36  app_trajectory.o(i.app_polyline_stop)
    app_polyline_task                        0x08007655   Thumb Code   330  app_trajectory.o(i.app_polyline_task)
    app_trajectory_check_arrival             0x08007909   Thumb Code    68  app_trajectory.o(i.app_trajectory_check_arrival)
    app_trajectory_init                      0x08007951   Thumb Code   112  app_trajectory.o(i.app_trajectory_init)
    app_vector_calc_next_point               0x080079c5   Thumb Code   154  app_trajectory.o(i.app_vector_calc_next_point)
    app_vector_reset_to_center               0x08007a69   Thumb Code   146  app_trajectory.o(i.app_vector_reset_to_center)
    app_vector_return_and_continue           0x08007b5d   Thumb Code   114  app_trajectory.o(i.app_vector_return_and_continue)
    app_vector_set_rect                      0x08007c69   Thumb Code    44  app_trajectory.o(i.app_vector_set_rect)
    app_vector_start                         0x08007cd5   Thumb Code    96  app_trajectory.o(i.app_vector_start)
    app_vector_stop                          0x08007d99   Thumb Code    36  app_trajectory.o(i.app_vector_stop)
    app_vector_task                          0x08007de5   Thumb Code   282  app_trajectory.o(i.app_vector_task)
    botton_task                              0x08008079   Thumb Code    44  app_botton.o(i.botton_task)
    bsp_get_systick                          0x080080ad   Thumb Code     8  main.o(i.bsp_get_systick)
    calc_edge_length                         0x080080b9   Thumb Code    60  app_trajectory.o(i.calc_edge_length)
    calc_motor_angle                         0x080080f5   Thumb Code    36  app_uasrt.o(i.calc_motor_angle)
    calc_pencil_center                       0x08008121   Thumb Code    54  app_trajectory.o(i.calc_pencil_center)
    calc_polyline_center                     0x0800815d   Thumb Code    62  app_trajectory.o(i.calc_polyline_center)
    calc_relative_angle                      0x080081a1   Thumb Code    62  app_uasrt.o(i.calc_relative_angle)
    calc_vector_center                       0x080081e9   Thumb Code    54  app_trajectory.o(i.calc_vector_center)
    check_motor_angle_limits                 0x08008225   Thumb Code   114  app_uasrt.o(i.check_motor_angle_limits)
    convert_to_pulse                         0x08008311   Thumb Code    30  app_laser_draw.o(i.convert_to_pulse)
    delay1_callback                          0x08008395   Thumb Code    26  main.o(i.delay1_callback)
    delay2_callback                          0x080083b9   Thumb Code    30  main.o(i.delay2_callback)
    delay3_callback                          0x080083e5   Thumb Code    30  main.o(i.delay3_callback)
    delay4_callback                          0x08008411   Thumb Code    38  main.o(i.delay4_callback)
    draw_circle                              0x0800843d   Thumb Code   222  app_laser_draw.o(i.draw_circle)
    draw_to_point                            0x0800852d   Thumb Code    24  app_laser_draw.o(i.draw_to_point)
    fabs                                     0x08008549   Thumb Code    24  fabs.o(i.fabs)
    hmi_parse_data                           0x08008561   Thumb Code    92  app_hmi.o(i.hmi_parse_data)
    hmi_parse_frame                          0x08008609   Thumb Code    52  app_hmi.o(i.hmi_parse_frame)
    hmi_process_command                      0x0800863d   Thumb Code   226  app_hmi.o(i.hmi_process_command)
    hmi_task                                 0x080087e9   Thumb Code    50  app_hmi.o(i.hmi_task)
    main                                     0x08008825   Thumb Code   174  main.o(i.main)
    maixcam_parse_data                       0x0800890d   Thumb Code   158  app_maixcam.o(i.maixcam_parse_data)
    maixcam_set_callback                     0x080089c1   Thumb Code    12  app_maixcam.o(i.maixcam_set_callback)
    maixcam_task                             0x080089d5   Thumb Code    92  app_maixcam.o(i.maixcam_task)
    move_to_point                            0x08008a55   Thumb Code    24  app_laser_draw.o(i.move_to_point)
    multiTimerInstall                        0x08008a71   Thumb Code    16  multitimer.o(i.multiTimerInstall)
    multiTimerStart                          0x08008a85   Thumb Code    84  multitimer.o(i.multiTimerStart)
    multiTimerStop                           0x08008add   Thumb Code    10  multitimer.o(i.multiTimerStop)
    multiTimerYield                          0x08008ae9   Thumb Code    58  multitimer.o(i.multiTimerYield)
    my_printf                                0x08008b29   Thumb Code    50  app_uasrt.o(i.my_printf)
    parse_x_motor_data                       0x08008b5d   Thumb Code   326  app_uasrt.o(i.parse_x_motor_data)
    parse_y_motor_data                       0x08008e45   Thumb Code   326  app_uasrt.o(i.parse_y_motor_data)
    pid_calculate_positional                 0x0800912d   Thumb Code   102  pid.o(i.pid_calculate_positional)
    pid_init                                 0x08009195   Thumb Code    42  pid.o(i.pid_init)
    pid_laser_coord_callback                 0x080091c5   Thumb Code    28  app_pid.o(i.pid_laser_coord_callback)
    pid_reset                                0x08009209   Thumb Code    34  pid.o(i.pid_reset)
    pid_set_limit                            0x08009231   Thumb Code     6  pid.o(i.pid_set_limit)
    pid_set_params                           0x08009237   Thumb Code     6  pid.o(i.pid_set_params)
    pid_set_target                           0x0800923d   Thumb Code     6  pid.o(i.pid_set_target)
    print_hex_array_x                        0x08009245   Thumb Code    48  app_uasrt.o(i.print_hex_array_x)
    print_hex_array_y                        0x08009291   Thumb Code    48  app_uasrt.o(i.print_hex_array_y)
    process_command                          0x080092dd   Thumb Code   188  app_uasrt.o(i.process_command)
    process_reset_command                    0x080093f9   Thumb Code    80  app_uasrt.o(i.process_reset_command)
    process_trajectory_command               0x08009499   Thumb Code   684  app_maixcam.o(i.process_trajectory_command)
    rose_polar_r                             0x080099c9   Thumb Code    40  app_laser_draw.o(i.rose_polar_r)
    rt_ringbuffer_data_len                   0x080099f1   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08009a21   Thumb Code   116  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08009a95   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08009ac5   Thumb Code   120  ringbuffer.o(i.rt_ringbuffer_put)
    save_initial_position                    0x08009b5d   Thumb Code    44  app_uasrt.o(i.save_initial_position)
    sine_function                            0x08009bb1   Thumb Code    32  app_laser_draw.o(i.sine_function)
    start_drawing                            0x08009bd1   Thumb Code   126  app_laser_draw.o(i.start_drawing)
    usart_task                               0x08009c5d   Thumb Code   164  app_uasrt.o(i.usart_task)
    user_task                                0x08009d35   Thumb Code    68  app_uasrt.o(i.user_task)
    _get_lc_ctype                            0x08009d7d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _get_lc_numeric                          0x08009da9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dneg                             0x08009dd5   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08009dd5   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x08009ddb   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x08009ddb   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08009de1   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x08009de7   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x08009ded   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08009ded   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08009e51   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08009e51   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __aeabi_ddiv                             0x08009fa1   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08009fa1   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x0800a251   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800a251   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x0800a2af   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800a2af   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x0800a2dd   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800a2dd   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x0800a305   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800a305   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800a459   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800a4f5   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x0800a501   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800a501   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800a519   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800a6b1   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800a6b1   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800a885   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800a885   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800a8db   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800a967   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800a96f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800a96f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800a971   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800a97b   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800a97f   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x0800a982   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x0800a98a   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800a99a   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x0800ad08   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800ad28   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800ad35   Data           0  lc_ctype_c.o(locale$$data)
    uart_flag                                0x20000000   Data           1  main.o(.data)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    key_val                                  0x2000001c   Data           1  app_botton.o(.data)
    key_old                                  0x2000001d   Data           1  app_botton.o(.data)
    key_down                                 0x2000001e   Data           1  app_botton.o(.data)
    key_up                                   0x2000001f   Data           1  app_botton.o(.data)
    motor_x                                  0x20000025   Data           1  app_pid.o(.data)
    motor_y                                  0x20000026   Data           1  app_pid.o(.data)
    target_x                                 0x20000028   Data           4  app_pid.o(.data)
    target_y                                 0x2000002c   Data           4  app_pid.o(.data)
    current_x                                0x20000030   Data           4  app_pid.o(.data)
    current_y                                0x20000034   Data           4  app_pid.o(.data)
    pid_params_x                             0x20000038   Data          36  app_pid.o(.data)
    pid_params_y                             0x2000005c   Data          36  app_pid.o(.data)
    x_angle_limit_flag                       0x2000008c   Data           1  app_uasrt.o(.data)
    y_angle_limit_flag                       0x2000008d   Data           1  app_uasrt.o(.data)
    motor_angle_limit_check_enabled          0x2000008e   Data           1  app_uasrt.o(.data)
    x_reference_initialized                  0x2000008f   Data           1  app_uasrt.o(.data)
    y_reference_initialized                  0x20000090   Data           1  app_uasrt.o(.data)
    x_initial_direction                      0x20000091   Data           1  app_uasrt.o(.data)
    y_initial_direction                      0x20000092   Data           1  app_uasrt.o(.data)
    initial_position_saved                   0x20000093   Data           1  app_uasrt.o(.data)
    x_motor_angle                            0x20000094   Data           4  app_uasrt.o(.data)
    y_motor_angle                            0x20000098   Data           4  app_uasrt.o(.data)
    x_reference_position                     0x2000009c   Data           4  app_uasrt.o(.data)
    y_reference_position                     0x200000a0   Data           4  app_uasrt.o(.data)
    x_relative_angle                         0x200000a4   Data           4  app_uasrt.o(.data)
    y_relative_angle                         0x200000a8   Data           4  app_uasrt.o(.data)
    x_initial_position                       0x200000ac   Data           4  app_uasrt.o(.data)
    y_initial_position                       0x200000b0   Data           4  app_uasrt.o(.data)
    mt_usart                                 0x200000c8   Data          24  main.o(.bss)
    mt_cam                                   0x200000e0   Data          24  main.o(.bss)
    mt_user                                  0x200000f8   Data          24  main.o(.bss)
    mt_botton                                0x20000110   Data          24  main.o(.bss)
    delay1_timer                             0x20000128   Data          24  main.o(.bss)
    delay2_timer                             0x20000140   Data          24  main.o(.bss)
    delay3_timer                             0x20000158   Data          24  main.o(.bss)
    delay4_timer                             0x20000170   Data          24  main.o(.bss)
    mt_hmi                                   0x20000188   Data          24  main.o(.bss)
    mt_polyline_trajectory                   0x200001a0   Data          24  main.o(.bss)
    hi2c1                                    0x200001b8   Data          84  i2c.o(.bss)
    hi2c2                                    0x2000020c   Data          84  i2c.o(.bss)
    htim1                                    0x20000260   Data          72  tim.o(.bss)
    htim2                                    0x200002a8   Data          72  tim.o(.bss)
    htim3                                    0x200002f0   Data          72  tim.o(.bss)
    htim4                                    0x20000338   Data          72  tim.o(.bss)
    htim8                                    0x20000380   Data          72  tim.o(.bss)
    htim12                                   0x200003c8   Data          72  tim.o(.bss)
    motor_x_buf                              0x20000410   Data         128  usart.o(.bss)
    motor_y_buf                              0x20000490   Data         128  usart.o(.bss)
    cam_rx_buf                               0x20000510   Data         128  usart.o(.bss)
    user_rx_buf                              0x20000590   Data         128  usart.o(.bss)
    hmi_rx_buf                               0x20000610   Data         128  usart.o(.bss)
    huart4                                   0x20000690   Data          72  usart.o(.bss)
    huart5                                   0x200006d8   Data          72  usart.o(.bss)
    huart1                                   0x20000720   Data          72  usart.o(.bss)
    huart2                                   0x20000768   Data          72  usart.o(.bss)
    huart3                                   0x200007b0   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x200007f8   Data          96  usart.o(.bss)
    hdma_uart5_rx                            0x20000858   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x200008b8   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x20000918   Data          96  usart.o(.bss)
    hdma_usart3_rx                           0x20000978   Data          96  usart.o(.bss)
    text01                                   0x200009d8   Data          40  app_oled.o(.bss)
    pid_x                                    0x20000a00   Data          60  app_pid.o(.bss)
    pid_y                                    0x20000a3c   Data          60  app_pid.o(.bss)
    pencil_trajectory                        0x20000aa8   Data          56  app_trajectory.o(.bss)
    vector_trajectory                        0x20000ae0   Data          56  app_trajectory.o(.bss)
    polyline_trajectory                      0x20000b18   Data         440  app_trajectory.o(.bss)
    mt_pencil_trajectory                     0x20000cd0   Data          24  app_trajectory.o(.bss)
    mt_vector_trajectory                     0x20000ce8   Data          24  app_trajectory.o(.bss)
    ringbuffer_pool_x                        0x20000d00   Data         128  app_uasrt.o(.bss)
    ringbuffer_pool_y                        0x20000d80   Data         128  app_uasrt.o(.bss)
    ringbuffer_pool_cam                      0x20000e00   Data         128  app_uasrt.o(.bss)
    ringbuffer_pool_user                     0x20000e80   Data         128  app_uasrt.o(.bss)
    ringbuffer_pool_hmi                      0x20000f00   Data         128  app_uasrt.o(.bss)
    output_buffer_x                          0x20000f80   Data         128  app_uasrt.o(.bss)
    output_buffer_y                          0x20001000   Data         128  app_uasrt.o(.bss)
    output_buffer_user                       0x20001080   Data         128  app_uasrt.o(.bss)
    ringbuffer_x                             0x20001100   Data          12  app_uasrt.o(.bss)
    ringbuffer_y                             0x2000110c   Data          12  app_uasrt.o(.bss)
    ringbuffer_user                          0x20001118   Data          12  app_uasrt.o(.bss)
    output_buffer_cam                        0x20001124   Data         128  app_uasrt.o(.bss)
    output_buffer_hmi                        0x200011a4   Data         128  app_uasrt.o(.bss)
    ringbuffer_cam                           0x20001224   Data          12  app_uasrt.o(.bss)
    ringbuffer_hmi                           0x20001230   Data          12  app_uasrt.o(.bss)
    __libspace_start                         0x20001290   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200012f0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000af18, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000ae54, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         4989  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         5469    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         5471    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         5473    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         5136    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         5125    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         5127    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         5132    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         5133    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         5134    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         5135    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         5140    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         5129    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         5130    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         5131    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         5128    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         5126    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         5137    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         5138    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         5139    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         5144    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         5145    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         5141    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         5123    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         5124    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         5142    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         5143    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         5255    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         5351    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         5364    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5367    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5370    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5372    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5374    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         5375    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x00000000   Code   RO         5377    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x0000000c   Code   RO         5378    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         5379    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         5381    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x0000000a   Code   RO         5382    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5383    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5385    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5387    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5389    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5391    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5393    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5395    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5397    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5401    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5403    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5405    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5407    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         5408    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         5440    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5450    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5452    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5454    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5457    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5460    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5462    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5465    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         5466    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         5041    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         5218    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         5230    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         5220    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         5221    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         5223    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         5224    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         5356    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         5410    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         5411    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         5412    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000314   0x08000314   0x000000ee   Code   RO         4954    .text               c_w.l(lludivv7m.o)
    0x08000402   0x08000402   0x00000002   PAD
    0x08000404   0x08000404   0x00000034   Code   RO         4956    .text               c_w.l(vsnprintf.o)
    0x08000438   0x08000438   0x0000003c   Code   RO         4958    .text               c_w.l(__0sscanf.o)
    0x08000474   0x08000474   0x0000014c   Code   RO         4960    .text               c_w.l(_scanf_int.o)
    0x080005c0   0x080005c0   0x000000e0   Code   RO         4962    .text               c_w.l(_scanf_str.o)
    0x080006a0   0x080006a0   0x0000001a   Code   RO         4964    .text               c_w.l(atoi.o)
    0x080006ba   0x080006ba   0x00000016   Code   RO         4966    .text               c_w.l(abort.o)
    0x080006d0   0x080006d0   0x0000000c   Code   RO         4968    .text               c_w.l(strtok.o)
    0x080006dc   0x080006dc   0x00000014   Code   RO         4971    .text               c_w.l(strchr.o)
    0x080006f0   0x080006f0   0x00000016   Code   RO         4973    .text               c_w.l(strrchr.o)
    0x08000706   0x08000706   0x0000003e   Code   RO         4975    .text               c_w.l(strlen.o)
    0x08000744   0x08000744   0x00000096   Code   RO         4977    .text               c_w.l(strncmp.o)
    0x080007da   0x080007da   0x0000008a   Code   RO         4979    .text               c_w.l(rt_memcpy_v6.o)
    0x08000864   0x08000864   0x00000064   Code   RO         4981    .text               c_w.l(rt_memcpy_w.o)
    0x080008c8   0x080008c8   0x00000044   Code   RO         4983    .text               c_w.l(rt_memclr.o)
    0x0800090c   0x0800090c   0x0000004e   Code   RO         4985    .text               c_w.l(rt_memclr_w.o)
    0x0800095a   0x0800095a   0x00000006   Code   RO         4987    .text               c_w.l(heapauxi.o)
    0x08000960   0x08000960   0x0000000c   Code   RO         5039    .text               c_w.l(sys_exit.o)
    0x0800096c   0x0800096c   0x00000008   Code   RO         5049    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000974   0x08000974   0x00000016   Code   RO         5051    .text               c_w.l(_rserrno.o)
    0x0800098a   0x0800098a   0x0000004e   Code   RO         5055    .text               c_w.l(_printf_pad.o)
    0x080009d8   0x080009d8   0x00000024   Code   RO         5057    .text               c_w.l(_printf_truncate.o)
    0x080009fc   0x080009fc   0x00000052   Code   RO         5059    .text               c_w.l(_printf_str.o)
    0x08000a4e   0x08000a4e   0x00000002   PAD
    0x08000a50   0x08000a50   0x00000078   Code   RO         5061    .text               c_w.l(_printf_dec.o)
    0x08000ac8   0x08000ac8   0x00000028   Code   RO         5063    .text               c_w.l(_printf_charcount.o)
    0x08000af0   0x08000af0   0x00000030   Code   RO         5065    .text               c_w.l(_printf_char_common.o)
    0x08000b20   0x08000b20   0x0000000a   Code   RO         5067    .text               c_w.l(_sputc.o)
    0x08000b2a   0x08000b2a   0x00000010   Code   RO         5069    .text               c_w.l(_snputc.o)
    0x08000b3a   0x08000b3a   0x00000002   PAD
    0x08000b3c   0x08000b3c   0x000000bc   Code   RO         5071    .text               c_w.l(_printf_wctomb.o)
    0x08000bf8   0x08000bf8   0x0000007c   Code   RO         5074    .text               c_w.l(_printf_longlong_dec.o)
    0x08000c74   0x08000c74   0x00000070   Code   RO         5080    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000ce4   0x08000ce4   0x00000094   Code   RO         5100    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000d78   0x08000d78   0x00000188   Code   RO         5120    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000f00   0x08000f00   0x0000001c   Code   RO         5146    .text               c_w.l(_chval.o)
    0x08000f1c   0x08000f1c   0x0000002c   Code   RO         5148    .text               c_w.l(scanf_char.o)
    0x08000f48   0x08000f48   0x00000040   Code   RO         5150    .text               c_w.l(_sgetc.o)
    0x08000f88   0x08000f88   0x00000070   Code   RO         5152    .text               c_w.l(strtol.o)
    0x08000ff8   0x08000ff8   0x0000000e   Code   RO         5154    .text               c_w.l(defsig_abrt_outer.o)
    0x08001006   0x08001006   0x00000002   PAD
    0x08001008   0x08001008   0x00000044   Code   RO         5158    .text               c_w.l(strtok_int.o)
    0x0800104c   0x0800104c   0x00000008   Code   RO         5211    .text               c_w.l(libspace.o)
    0x08001054   0x08001054   0x00000002   Code   RO         5214    .text               c_w.l(use_no_semi.o)
    0x08001056   0x08001056   0x00000000   Code   RO         5216    .text               c_w.l(indicate_semi.o)
    0x08001056   0x08001056   0x00000002   PAD
    0x08001058   0x08001058   0x00000010   Code   RO         5232    .text               c_w.l(rt_ctype_table.o)
    0x08001068   0x08001068   0x0000008a   Code   RO         5238    .text               c_w.l(lludiv10.o)
    0x080010f2   0x080010f2   0x00000012   Code   RO         5240    .text               c_w.l(isspace.o)
    0x08001104   0x08001104   0x000000b2   Code   RO         5242    .text               c_w.l(_printf_intcommon.o)
    0x080011b6   0x080011b6   0x0000041e   Code   RO         5244    .text               c_w.l(_printf_fp_dec.o)
    0x080015d4   0x080015d4   0x000002fc   Code   RO         5246    .text               c_w.l(_printf_fp_hex.o)
    0x080018d0   0x080018d0   0x0000002c   Code   RO         5251    .text               c_w.l(_printf_char.o)
    0x080018fc   0x080018fc   0x0000002c   Code   RO         5253    .text               c_w.l(_printf_wchar.o)
    0x08001928   0x08001928   0x00000374   Code   RO         5256    .text               c_w.l(_scanf.o)
    0x08001c9c   0x08001c9c   0x0000009e   Code   RO         5258    .text               c_w.l(_strtoul.o)
    0x08001d3a   0x08001d3a   0x00000040   Code   RO         5260    .text               c_w.l(_wcrtomb.o)
    0x08001d7a   0x08001d7a   0x0000000a   Code   RO         5262    .text               c_w.l(defsig_exit.o)
    0x08001d84   0x08001d84   0x00000030   Code   RO         5264    .text               c_w.l(defsig_abrt_inner.o)
    0x08001db4   0x08001db4   0x00000020   Code   RO         5266    .text               c_w.l(strcspn.o)
    0x08001dd4   0x08001dd4   0x0000001c   Code   RO         5268    .text               c_w.l(strspn.o)
    0x08001df0   0x08001df0   0x0000004a   Code   RO         5296    .text               c_w.l(sys_stackheap_outer.o)
    0x08001e3a   0x08001e3a   0x00000002   PAD
    0x08001e3c   0x08001e3c   0x00000008   Code   RO         5301    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001e44   0x08001e44   0x00000080   Code   RO         5303    .text               c_w.l(_printf_fp_infnan.o)
    0x08001ec4   0x08001ec4   0x000000e4   Code   RO         5305    .text               c_w.l(bigflt0.o)
    0x08001fa8   0x08001fa8   0x00000012   Code   RO         5336    .text               c_w.l(exit.o)
    0x08001fba   0x08001fba   0x00000032   Code   RO         5340    .text               c_w.l(defsig_general.o)
    0x08001fec   0x08001fec   0x0000000e   Code   RO         5354    .text               c_w.l(sys_wrch.o)
    0x08001ffa   0x08001ffa   0x00000002   PAD
    0x08001ffc   0x08001ffc   0x00000080   Code   RO         5362    .text               c_w.l(strcmpv7m.o)
    0x0800207c   0x0800207c   0x0000003e   Code   RO         5308    CL$$btod_d2e        c_w.l(btod.o)
    0x080020ba   0x080020ba   0x00000046   Code   RO         5310    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002100   0x08002100   0x00000060   Code   RO         5309    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002160   0x08002160   0x00000338   Code   RO         5318    CL$$btod_div_common  c_w.l(btod.o)
    0x08002498   0x08002498   0x000000dc   Code   RO         5315    CL$$btod_e2e        c_w.l(btod.o)
    0x08002574   0x08002574   0x0000002a   Code   RO         5312    CL$$btod_ediv       c_w.l(btod.o)
    0x0800259e   0x0800259e   0x0000002a   Code   RO         5311    CL$$btod_emul       c_w.l(btod.o)
    0x080025c8   0x080025c8   0x00000244   Code   RO         5317    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800280c   0x0800280c   0x00000002   Code   RO          560    i.BusFault_Handler  stm32f4xx_it.o
    0x0800280e   0x0800280e   0x00000002   PAD
    0x08002810   0x08002810   0x0000000c   Code   RO          561    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x0800281c   0x0800281c   0x0000000c   Code   RO          562    i.DMA1_Stream1_IRQHandler  stm32f4xx_it.o
    0x08002828   0x08002828   0x0000000c   Code   RO          563    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x08002834   0x08002834   0x0000000c   Code   RO          564    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08002840   0x08002840   0x0000000c   Code   RO          565    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x0800284c   0x0800284c   0x00000028   Code   RO         1638    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08002874   0x08002874   0x00000054   Code   RO         1639    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x080028c8   0x080028c8   0x00000028   Code   RO         1640    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080028f0   0x080028f0   0x00000002   Code   RO          566    i.DebugMon_Handler  stm32f4xx_it.o
    0x080028f2   0x080028f2   0x00000036   Code   RO         3825    i.Emm_V5_En_Control  emm_v5.o
    0x08002928   0x08002928   0x000001ec   Code   RO         3831    i.Emm_V5_Parse_Response  emm_v5.o
    0x08002b14   0x08002b14   0x0000006e   Code   RO         3832    i.Emm_V5_Pos_Control  emm_v5.o
    0x08002b82   0x08002b82   0x0000008c   Code   RO         3833    i.Emm_V5_Read_Sys_Params  emm_v5.o
    0x08002c0e   0x08002c0e   0x0000002e   Code   RO         3835    i.Emm_V5_Reset_CurPos_To_Zero  emm_v5.o
    0x08002c3c   0x08002c3c   0x00000032   Code   RO         3836    i.Emm_V5_Stop_Now   emm_v5.o
    0x08002c6e   0x08002c6e   0x00000042   Code   RO         3838    i.Emm_V5_Vel_Control  emm_v5.o
    0x08002cb0   0x08002cb0   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08002cb4   0x08002cb4   0x00000092   Code   RO         1641    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08002d46   0x08002d46   0x00000024   Code   RO         1642    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002d6a   0x08002d6a   0x00000002   PAD
    0x08002d6c   0x08002d6c   0x000001a0   Code   RO         1646    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002f0c   0x08002f0c   0x000000d4   Code   RO         1647    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002fe0   0x08002fe0   0x0000006e   Code   RO         1651    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800304e   0x0800304e   0x00000002   PAD
    0x08003050   0x08003050   0x000001f0   Code   RO         1534    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08003240   0x08003240   0x0000000a   Code   RO         1538    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x0800324a   0x0800324a   0x00000002   PAD
    0x0800324c   0x0800324c   0x0000000c   Code   RO         2084    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003258   0x08003258   0x00000188   Code   RO          735    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080033e0   0x080033e0   0x000000ac   Code   RO          331    i.HAL_I2C_MspInit   i2c.o
    0x0800348c   0x0800348c   0x00000010   Code   RO         2090    i.HAL_IncTick       stm32f4xx_hal.o
    0x0800349c   0x0800349c   0x00000034   Code   RO         2091    i.HAL_Init          stm32f4xx_hal.o
    0x080034d0   0x080034d0   0x00000040   Code   RO         2092    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003510   0x08003510   0x00000030   Code   RO          700    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003540   0x08003540   0x0000001a   Code   RO         1926    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800355a   0x0800355a   0x00000002   PAD
    0x0800355c   0x0800355c   0x00000040   Code   RO         1932    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800359c   0x0800359c   0x00000024   Code   RO         1933    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080035c0   0x080035c0   0x00000134   Code   RO         1180    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080036f4   0x080036f4   0x00000020   Code   RO         1187    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003714   0x08003714   0x00000020   Code   RO         1188    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003734   0x08003734   0x00000060   Code   RO         1189    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003794   0x08003794   0x0000036c   Code   RO         1192    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003b00   0x08003b00   0x00000028   Code   RO         1937    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003b28   0x08003b28   0x00000054   Code   RO         3038    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08003b7c   0x08003b7c   0x00000090   Code   RO         3054    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003c0c   0x08003c0c   0x0000005a   Code   RO         2331    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003c66   0x08003c66   0x00000002   PAD
    0x08003c68   0x08003c68   0x00000028   Code   RO          379    i.HAL_TIM_Base_MspInit  tim.o
    0x08003c90   0x08003c90   0x000000dc   Code   RO         2340    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003d6c   0x08003d6c   0x000000a4   Code   RO         2352    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08003e10   0x08003e10   0x000000a8   Code   RO          381    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08003eb8   0x08003eb8   0x000000e4   Code   RO          382    i.HAL_TIM_MspPostInit  tim.o
    0x08003f9c   0x08003f9c   0x000000cc   Code   RO         2403    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08004068   0x08004068   0x0000005a   Code   RO         2406    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x080040c2   0x080040c2   0x00000002   PAD
    0x080040c4   0x080040c4   0x0000005c   Code   RO          384    i.HAL_TIM_PWM_MspInit  tim.o
    0x08004120   0x08004120   0x0000004a   Code   RO         3312    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x0800416a   0x0800416a   0x00000002   PAD
    0x0800416c   0x0800416c   0x00000178   Code   RO          480    i.HAL_UARTEx_RxEventCallback  usart.o
    0x080042e4   0x080042e4   0x00000002   Code   RO         3328    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x080042e6   0x080042e6   0x00000002   PAD
    0x080042e8   0x080042e8   0x00000280   Code   RO         3331    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08004568   0x08004568   0x00000064   Code   RO         3332    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x080045cc   0x080045cc   0x000002f4   Code   RO          482    i.HAL_UART_MspInit  usart.o
    0x080048c0   0x080048c0   0x00000002   Code   RO         3338    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x080048c2   0x080048c2   0x00000002   Code   RO         3339    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x080048c4   0x080048c4   0x000000a0   Code   RO         3340    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004964   0x08004964   0x00000002   Code   RO         3343    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004966   0x08004966   0x00000002   Code   RO          567    i.HardFault_Handler  stm32f4xx_it.o
    0x08004968   0x08004968   0x0000007c   Code   RO          306    i.MX_DMA_Init       dma.o
    0x080049e4   0x080049e4   0x000000f8   Code   RO          282    i.MX_GPIO_Init      gpio.o
    0x08004adc   0x08004adc   0x00000040   Code   RO          332    i.MX_I2C1_Init      i2c.o
    0x08004b1c   0x08004b1c   0x00000040   Code   RO          333    i.MX_I2C2_Init      i2c.o
    0x08004b5c   0x08004b5c   0x00000058   Code   RO          385    i.MX_TIM12_Init     tim.o
    0x08004bb4   0x08004bb4   0x0000006c   Code   RO          386    i.MX_TIM1_Init      tim.o
    0x08004c20   0x08004c20   0x00000064   Code   RO          387    i.MX_TIM2_Init      tim.o
    0x08004c84   0x08004c84   0x00000070   Code   RO          388    i.MX_TIM3_Init      tim.o
    0x08004cf4   0x08004cf4   0x00000070   Code   RO          389    i.MX_TIM4_Init      tim.o
    0x08004d64   0x08004d64   0x000000c8   Code   RO          390    i.MX_TIM8_Init      tim.o
    0x08004e2c   0x08004e2c   0x00000050   Code   RO          483    i.MX_UART4_Init     usart.o
    0x08004e7c   0x08004e7c   0x00000050   Code   RO          484    i.MX_UART5_Init     usart.o
    0x08004ecc   0x08004ecc   0x00000050   Code   RO          485    i.MX_USART1_UART_Init  usart.o
    0x08004f1c   0x08004f1c   0x00000050   Code   RO          486    i.MX_USART2_UART_Init  usart.o
    0x08004f6c   0x08004f6c   0x00000050   Code   RO          487    i.MX_USART3_UART_Init  usart.o
    0x08004fbc   0x08004fbc   0x00000002   Code   RO          568    i.MemManage_Handler  stm32f4xx_it.o
    0x08004fbe   0x08004fbe   0x00000002   PAD
    0x08004fc0   0x08004fc0   0x0000002c   Code   RO         4167    i.Motor_Init        app_motor.o
    0x08004fec   0x08004fec   0x00000054   Code   RO         4168    i.Motor_Set_Position  app_motor.o
    0x08005040   0x08005040   0x00000080   Code   RO         4169    i.Motor_Set_Speed   app_motor.o
    0x080050c0   0x080050c0   0x00000024   Code   RO         4170    i.Motor_Stop        app_motor.o
    0x080050e4   0x080050e4   0x00000002   Code   RO          569    i.NMI_Handler       stm32f4xx_it.o
    0x080050e6   0x080050e6   0x00000002   Code   RO          570    i.PendSV_Handler    stm32f4xx_it.o
    0x080050e8   0x080050e8   0x00000002   Code   RO          571    i.SVC_Handler       stm32f4xx_it.o
    0x080050ea   0x080050ea   0x00000004   Code   RO          572    i.SysTick_Handler   stm32f4xx_it.o
    0x080050ee   0x080050ee   0x00000002   PAD
    0x080050f0   0x080050f0   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08005184   0x08005184   0x00000010   Code   RO         3666    i.SystemInit        system_stm32f4xx.o
    0x08005194   0x08005194   0x000000d0   Code   RO         2424    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005264   0x08005264   0x00000014   Code   RO         2435    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08005278   0x08005278   0x00000010   Code   RO         2436    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08005288   0x08005288   0x00000060   Code   RO         2437    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x080052e8   0x080052e8   0x0000006c   Code   RO         2438    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08005354   0x08005354   0x00000068   Code   RO         2439    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x080053bc   0x080053bc   0x00000050   Code   RO         2440    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x0800540c   0x0800540c   0x00000022   Code   RO         2442    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x0800542e   0x0800542e   0x00000024   Code   RO         2444    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005452   0x08005452   0x00000002   PAD
    0x08005454   0x08005454   0x0000002c   Code   RO          573    i.UART4_IRQHandler  stm32f4xx_it.o
    0x08005480   0x08005480   0x0000002c   Code   RO          574    i.UART5_IRQHandler  stm32f4xx_it.o
    0x080054ac   0x080054ac   0x0000000e   Code   RO         3345    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080054ba   0x080054ba   0x0000004a   Code   RO         3346    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08005504   0x08005504   0x00000086   Code   RO         3347    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x0800558a   0x0800558a   0x0000001e   Code   RO         3349    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x080055a8   0x080055a8   0x0000004e   Code   RO         3355    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x080055f6   0x080055f6   0x0000001c   Code   RO         3356    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005612   0x08005612   0x000000c2   Code   RO         3357    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080056d4   0x080056d4   0x0000010c   Code   RO         3358    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080057e0   0x080057e0   0x000000a0   Code   RO         3359    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08005880   0x08005880   0x00000072   Code   RO         3361    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x080058f2   0x080058f2   0x00000002   PAD
    0x080058f4   0x080058f4   0x0000002c   Code   RO          575    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08005920   0x08005920   0x0000002c   Code   RO          576    i.USART2_IRQHandler  stm32f4xx_it.o
    0x0800594c   0x0800594c   0x0000002c   Code   RO          577    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08005978   0x08005978   0x00000002   Code   RO          578    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800597a   0x0800597a   0x00000030   Code   RO         5292    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080059aa   0x080059aa   0x00000020   Code   RO         1939    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080059ca   0x080059ca   0x00000006   PAD
    0x080059d0   0x080059d0   0x000000c8   Code   RO         5003    i.__hardfp_cos      m_wm.l(cos.o)
    0x08005a98   0x08005a98   0x000000c8   Code   RO         5015    i.__hardfp_sin      m_wm.l(sin.o)
    0x08005b60   0x08005b60   0x0000007a   Code   RO         5027    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08005bda   0x08005bda   0x00000006   PAD
    0x08005be0   0x08005be0   0x00000438   Code   RO         5200    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08006018   0x08006018   0x00000170   Code   RO         5183    i.__kernel_cos      m_wm.l(cos_i.o)
    0x08006188   0x08006188   0x000000f8   Code   RO         5294    i.__kernel_poly     m_wm.l(poly.o)
    0x08006280   0x08006280   0x00000130   Code   RO         5205    i.__kernel_sin      m_wm.l(sin_i.o)
    0x080063b0   0x080063b0   0x00000014   Code   RO         5187    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x080063c4   0x080063c4   0x00000004   PAD
    0x080063c8   0x080063c8   0x00000020   Code   RO         5189    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080063e8   0x080063e8   0x00000020   Code   RO         5192    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08006408   0x08006408   0x0000000e   Code   RO         5113    i._is_digit         c_w.l(__printf_wp.o)
    0x08006416   0x08006416   0x00000002   PAD
    0x08006418   0x08006418   0x00000010   Code   RO         4791    i.app_laser_draw_init  app_laser_draw.o
    0x08006428   0x08006428   0x0000016c   Code   RO         4792    i.app_laser_draw_task  app_laser_draw.o
    0x08006594   0x08006594   0x00000070   Code   RO         4451    i.app_pencil_calc_next_point  app_trajectory.o
    0x08006604   0x08006604   0x000000f0   Code   RO         4452    i.app_pencil_reset_to_center  app_trajectory.o
    0x080066f4   0x080066f4   0x000000f8   Code   RO         4454    i.app_pencil_return_and_continue  app_trajectory.o
    0x080067ec   0x080067ec   0x00000070   Code   RO         4455    i.app_pencil_set_rect  app_trajectory.o
    0x0800685c   0x0800685c   0x000000c4   Code   RO         4456    i.app_pencil_start  app_trajectory.o
    0x08006920   0x08006920   0x0000004c   Code   RO         4457    i.app_pencil_stop   app_trajectory.o
    0x0800696c   0x0800696c   0x00000294   Code   RO         4458    i.app_pencil_task   app_trajectory.o
    0x08006c00   0x08006c00   0x00000114   Code   RO         4332    i.app_pid_calc      app_pid.o
    0x08006d14   0x08006d14   0x0000005c   Code   RO         4333    i.app_pid_init      app_pid.o
    0x08006d70   0x08006d70   0x00000024   Code   RO         4334    i.app_pid_limit_integral  app_pid.o
    0x08006d94   0x08006d94   0x000001d4   Code   RO         4335    i.app_pid_parse_cmd  app_pid.o
    0x08006f68   0x08006f68   0x0000006c   Code   RO         4337    i.app_pid_report    app_pid.o
    0x08006fd4   0x08006fd4   0x00000020   Code   RO         4338    i.app_pid_report_task  app_pid.o
    0x08006ff4   0x08006ff4   0x00000034   Code   RO         4339    i.app_pid_set_target  app_pid.o
    0x08007028   0x08007028   0x00000034   Code   RO         4340    i.app_pid_set_x_params  app_pid.o
    0x0800705c   0x0800705c   0x00000034   Code   RO         4341    i.app_pid_set_y_params  app_pid.o
    0x08007090   0x08007090   0x00000078   Code   RO         4342    i.app_pid_start     app_pid.o
    0x08007108   0x08007108   0x00000034   Code   RO         4343    i.app_pid_stop      app_pid.o
    0x0800713c   0x0800713c   0x00000048   Code   RO         4344    i.app_pid_task      app_pid.o
    0x08007184   0x08007184   0x0000001c   Code   RO         4345    i.app_pid_update_position  app_pid.o
    0x080071a0   0x080071a0   0x0000008c   Code   RO         4459    i.app_polyline_calc_next_point  app_trajectory.o
    0x0800722c   0x0800722c   0x000000e0   Code   RO         4460    i.app_polyline_reset_to_center  app_trajectory.o
    0x0800730c   0x0800730c   0x000000b0   Code   RO         4462    i.app_polyline_return_and_continue  app_trajectory.o
    0x080073bc   0x080073bc   0x000000c4   Code   RO         4463    i.app_polyline_set_points  app_trajectory.o
    0x08007480   0x08007480   0x00000078   Code   RO         4464    i.app_polyline_smooth_target  app_trajectory.o
    0x080074f8   0x080074f8   0x00000108   Code   RO         4465    i.app_polyline_start  app_trajectory.o
    0x08007600   0x08007600   0x00000054   Code   RO         4466    i.app_polyline_stop  app_trajectory.o
    0x08007654   0x08007654   0x000002b4   Code   RO         4467    i.app_polyline_task  app_trajectory.o
    0x08007908   0x08007908   0x00000048   Code   RO         4468    i.app_trajectory_check_arrival  app_trajectory.o
    0x08007950   0x08007950   0x00000074   Code   RO         4469    i.app_trajectory_init  app_trajectory.o
    0x080079c4   0x080079c4   0x000000a4   Code   RO         4470    i.app_vector_calc_next_point  app_trajectory.o
    0x08007a68   0x08007a68   0x000000f4   Code   RO         4471    i.app_vector_reset_to_center  app_trajectory.o
    0x08007b5c   0x08007b5c   0x0000010c   Code   RO         4473    i.app_vector_return_and_continue  app_trajectory.o
    0x08007c68   0x08007c68   0x0000006c   Code   RO         4474    i.app_vector_set_rect  app_trajectory.o
    0x08007cd4   0x08007cd4   0x000000c4   Code   RO         4475    i.app_vector_start  app_trajectory.o
    0x08007d98   0x08007d98   0x0000004c   Code   RO         4476    i.app_vector_stop   app_trajectory.o
    0x08007de4   0x08007de4   0x00000294   Code   RO         4477    i.app_vector_task   app_trajectory.o
    0x08008078   0x08008078   0x00000034   Code   RO         3986    i.botton_task       app_botton.o
    0x080080ac   0x080080ac   0x0000000c   Code   RO           15    i.bsp_get_systick   main.o
    0x080080b8   0x080080b8   0x0000003c   Code   RO         4478    i.calc_edge_length  app_trajectory.o
    0x080080f4   0x080080f4   0x0000002c   Code   RO         4668    i.calc_motor_angle  app_uasrt.o
    0x08008120   0x08008120   0x0000003c   Code   RO         4479    i.calc_pencil_center  app_trajectory.o
    0x0800815c   0x0800815c   0x00000044   Code   RO         4480    i.calc_polyline_center  app_trajectory.o
    0x080081a0   0x080081a0   0x00000048   Code   RO         4669    i.calc_relative_angle  app_uasrt.o
    0x080081e8   0x080081e8   0x0000003c   Code   RO         4481    i.calc_vector_center  app_trajectory.o
    0x08008224   0x08008224   0x000000ec   Code   RO         4670    i.check_motor_angle_limits  app_uasrt.o
    0x08008310   0x08008310   0x00000024   Code   RO         4796    i.convert_to_pulse  app_laser_draw.o
    0x08008334   0x08008334   0x00000060   Code   RO         4118    i.default_laser_callback  app_maixcam.o
    0x08008394   0x08008394   0x00000024   Code   RO           16    i.delay1_callback   main.o
    0x080083b8   0x080083b8   0x0000002c   Code   RO           17    i.delay2_callback   main.o
    0x080083e4   0x080083e4   0x0000002c   Code   RO           18    i.delay3_callback   main.o
    0x08008410   0x08008410   0x0000002c   Code   RO           19    i.delay4_callback   main.o
    0x0800843c   0x0800843c   0x000000f0   Code   RO         4798    i.draw_circle       app_laser_draw.o
    0x0800852c   0x0800852c   0x0000001c   Code   RO         4801    i.draw_to_point     app_laser_draw.o
    0x08008548   0x08008548   0x00000018   Code   RO         5288    i.fabs              m_wm.l(fabs.o)
    0x08008560   0x08008560   0x000000a8   Code   RO         4071    i.hmi_parse_data    app_hmi.o
    0x08008608   0x08008608   0x00000034   Code   RO         4072    i.hmi_parse_frame   app_hmi.o
    0x0800863c   0x0800863c   0x000001ac   Code   RO         4073    i.hmi_process_command  app_hmi.o
    0x080087e8   0x080087e8   0x0000003c   Code   RO         4074    i.hmi_task          app_hmi.o
    0x08008824   0x08008824   0x000000e8   Code   RO           20    i.main              main.o
    0x0800890c   0x0800890c   0x000000b4   Code   RO         4119    i.maixcam_parse_data  app_maixcam.o
    0x080089c0   0x080089c0   0x00000014   Code   RO         4120    i.maixcam_set_callback  app_maixcam.o
    0x080089d4   0x080089d4   0x00000080   Code   RO         4121    i.maixcam_task      app_maixcam.o
    0x08008a54   0x08008a54   0x0000001c   Code   RO         4807    i.move_to_point     app_laser_draw.o
    0x08008a70   0x08008a70   0x00000014   Code   RO         3782    i.multiTimerInstall  multitimer.o
    0x08008a84   0x08008a84   0x00000058   Code   RO         3783    i.multiTimerStart   multitimer.o
    0x08008adc   0x08008adc   0x0000000a   Code   RO         3784    i.multiTimerStop    multitimer.o
    0x08008ae6   0x08008ae6   0x00000002   PAD
    0x08008ae8   0x08008ae8   0x00000040   Code   RO         3785    i.multiTimerYield   multitimer.o
    0x08008b28   0x08008b28   0x00000032   Code   RO         4671    i.my_printf         app_uasrt.o
    0x08008b5a   0x08008b5a   0x00000002   PAD
    0x08008b5c   0x08008b5c   0x000002e8   Code   RO         4672    i.parse_x_motor_data  app_uasrt.o
    0x08008e44   0x08008e44   0x000002e8   Code   RO         4673    i.parse_y_motor_data  app_uasrt.o
    0x0800912c   0x0800912c   0x00000066   Code   RO         3928    i.pid_calculate_positional  pid.o
    0x08009192   0x08009192   0x00000002   PAD
    0x08009194   0x08009194   0x00000030   Code   RO         3929    i.pid_init          pid.o
    0x080091c4   0x080091c4   0x0000001c   Code   RO         4346    i.pid_laser_coord_callback  app_pid.o
    0x080091e0   0x080091e0   0x00000026   Code   RO         3930    i.pid_out_limit     pid.o
    0x08009206   0x08009206   0x00000002   PAD
    0x08009208   0x08009208   0x00000028   Code   RO         3931    i.pid_reset         pid.o
    0x08009230   0x08009230   0x00000006   Code   RO         3932    i.pid_set_limit     pid.o
    0x08009236   0x08009236   0x00000006   Code   RO         3933    i.pid_set_params    pid.o
    0x0800923c   0x0800923c   0x00000006   Code   RO         3934    i.pid_set_target    pid.o
    0x08009242   0x08009242   0x00000002   PAD
    0x08009244   0x08009244   0x0000004c   Code   RO         4674    i.print_hex_array_x  app_uasrt.o
    0x08009290   0x08009290   0x0000004c   Code   RO         4675    i.print_hex_array_y  app_uasrt.o
    0x080092dc   0x080092dc   0x0000011c   Code   RO         4676    i.process_command   app_uasrt.o
    0x080093f8   0x080093f8   0x000000a0   Code   RO         4677    i.process_reset_command  app_uasrt.o
    0x08009498   0x08009498   0x00000514   Code   RO         4122    i.process_trajectory_command  app_maixcam.o
    0x080099ac   0x080099ac   0x0000001c   Code   RO         3786    i.removeTimer       multitimer.o
    0x080099c8   0x080099c8   0x00000028   Code   RO         4809    i.rose_polar_r      app_laser_draw.o
    0x080099f0   0x080099f0   0x00000030   Code   RO         3700    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08009a20   0x08009a20   0x00000074   Code   RO         3701    i.rt_ringbuffer_get  ringbuffer.o
    0x08009a94   0x08009a94   0x00000030   Code   RO         3703    i.rt_ringbuffer_init  ringbuffer.o
    0x08009ac4   0x08009ac4   0x00000078   Code   RO         3705    i.rt_ringbuffer_put  ringbuffer.o
    0x08009b3c   0x08009b3c   0x00000020   Code   RO         3710    i.rt_ringbuffer_status  ringbuffer.o
    0x08009b5c   0x08009b5c   0x00000054   Code   RO         4678    i.save_initial_position  app_uasrt.o
    0x08009bb0   0x08009bb0   0x00000020   Code   RO         4810    i.sine_function     app_laser_draw.o
    0x08009bd0   0x08009bd0   0x0000008c   Code   RO         4812    i.start_drawing     app_laser_draw.o
    0x08009c5c   0x08009c5c   0x000000d8   Code   RO         4679    i.usart_task        app_uasrt.o
    0x08009d34   0x08009d34   0x00000048   Code   RO         4680    i.user_task         app_uasrt.o
    0x08009d7c   0x08009d7c   0x0000002c   Code   RO         5331    locale$$code        c_w.l(lc_ctype_c.o)
    0x08009da8   0x08009da8   0x0000002c   Code   RO         5334    locale$$code        c_w.l(lc_numeric_c.o)
    0x08009dd4   0x08009dd4   0x00000018   Code   RO         5160    x$fpl$basic         fz_wm.l(basic.o)
    0x08009dec   0x08009dec   0x00000062   Code   RO         4991    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08009e4e   0x08009e4e   0x00000002   PAD
    0x08009e50   0x08009e50   0x00000150   Code   RO         4993    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08009fa0   0x08009fa0   0x000002b0   Code   RO         5273    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x0800a250   0x0800a250   0x0000005e   Code   RO         5276    x$fpl$dfix          fz_wm.l(dfix.o)
    0x0800a2ae   0x0800a2ae   0x0000002e   Code   RO         5281    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x0800a2dc   0x0800a2dc   0x00000026   Code   RO         5280    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800a302   0x0800a302   0x00000002   PAD
    0x0800a304   0x0800a304   0x00000154   Code   RO         4999    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800a458   0x0800a458   0x0000009c   Code   RO         5164    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800a4f4   0x0800a4f4   0x0000000c   Code   RO         5166    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800a500   0x0800a500   0x00000016   Code   RO         4994    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800a516   0x0800a516   0x00000002   PAD
    0x0800a518   0x0800a518   0x00000198   Code   RO         5168    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x0800a6b0   0x0800a6b0   0x000001d4   Code   RO         4995    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800a884   0x0800a884   0x00000056   Code   RO         5001    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800a8da   0x0800a8da   0x0000008c   Code   RO         5170    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800a966   0x0800a966   0x0000000a   Code   RO         5432    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800a970   0x0800a970   0x0000000a   Code   RO         5172    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800a97a   0x0800a97a   0x00000004   Code   RO         5174    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800a97e   0x0800a97e   0x00000004   Code   RO         5176    x$fpl$printf2       fz_wm.l(printf2.o)
    0x0800a982   0x0800a982   0x00000000   Code   RO         5182    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800a982   0x0800a982   0x00000008   Data   RO         1653    .constdata          stm32f4xx_hal_dma.o
    0x0800a98a   0x0800a98a   0x00000010   Data   RO         3667    .constdata          system_stm32f4xx.o
    0x0800a99a   0x0800a99a   0x00000008   Data   RO         3668    .constdata          system_stm32f4xx.o
    0x0800a9a2   0x0800a9a2   0x00000002   PAD
    0x0800a9a4   0x0800a9a4   0x00000070   Data   RO         4075    .constdata          app_hmi.o
    0x0800aa14   0x0800aa14   0x00000008   Data   RO         5072    .constdata          c_w.l(_printf_wctomb.o)
    0x0800aa1c   0x0800aa1c   0x00000028   Data   RO         5101    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800aa44   0x0800aa44   0x00000011   Data   RO         5121    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800aa55   0x0800aa55   0x00000003   PAD
    0x0800aa58   0x0800aa58   0x00000030   Data   RO         5184    .constdata          m_wm.l(cos_i.o)
    0x0800aa88   0x0800aa88   0x000000c8   Data   RO         5202    .constdata          m_wm.l(rred.o)
    0x0800ab50   0x0800ab50   0x00000028   Data   RO         5206    .constdata          m_wm.l(sin_i.o)
    0x0800ab78   0x0800ab78   0x00000026   Data   RO         5247    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800ab9e   0x0800ab9e   0x00000002   PAD
    0x0800aba0   0x0800aba0   0x00000094   Data   RO         5306    .constdata          c_w.l(bigflt0.o)
    0x0800ac34   0x0800ac34   0x0000004c   Data   RO         4483    .conststring        app_trajectory.o
    0x0800ac80   0x0800ac80   0x00000087   Data   RO         4691    .conststring        app_uasrt.o
    0x0800ad07   0x0800ad07   0x00000001   PAD
    0x0800ad08   0x0800ad08   0x00000020   Data   RO         5467    Region$$Table       anon$$obj.o
    0x0800ad28   0x0800ad28   0x00000110   Data   RO         5330    locale$$data        c_w.l(lc_ctype_c.o)
    0x0800ae38   0x0800ae38   0x0000001c   Data   RO         5333    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800ae54, Size: 0x000029f0, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800ae54   0x00000001   Data   RW           30    .data               main.o
    0x20000001   0x0800ae55   0x00000003   PAD
    0x20000004   0x0800ae58   0x0000000c   Data   RW         2098    .data               stm32f4xx_hal.o
    0x20000010   0x0800ae64   0x00000004   Data   RW         3669    .data               system_stm32f4xx.o
    0x20000014   0x0800ae68   0x00000008   Data   RW         3787    .data               multitimer.o
    0x2000001c   0x0800ae70   0x00000004   Data   RW         3989    .data               app_botton.o
    0x20000020   0x0800ae74   0x00000004   Data   RW         4123    .data               app_maixcam.o
    0x20000024   0x0800ae78   0x0000005c   Data   RW         4348    .data               app_pid.o
    0x20000080   0x0800aed4   0x00000009   Data   RW         4484    .data               app_trajectory.o
    0x20000089   0x0800aedd   0x00000003   PAD
    0x2000008c   0x0800aee0   0x00000028   Data   RW         4692    .data               app_uasrt.o
    0x200000b4   0x0800af08   0x0000000c   Data   RW         4816    .data               app_laser_draw.o
    0x200000c0   0x0800af14   0x00000004   Data   RW         4969    .data               c_w.l(strtok.o)
    0x200000c4   0x0800af18   0x00000004   PAD
    0x200000c8        -       0x00000018   Zero   RW           23    .bss                main.o
    0x200000e0        -       0x00000018   Zero   RW           24    .bss                main.o
    0x200000f8        -       0x00000018   Zero   RW           26    .bss                main.o
    0x20000110        -       0x00000078   Zero   RW           27    .bss                main.o
    0x20000188        -       0x00000018   Zero   RW           28    .bss                main.o
    0x200001a0        -       0x00000018   Zero   RW           29    .bss                main.o
    0x200001b8        -       0x000000a8   Zero   RW          334    .bss                i2c.o
    0x20000260        -       0x000001b0   Zero   RW          391    .bss                tim.o
    0x20000410        -       0x000005c8   Zero   RW          488    .bss                usart.o
    0x200009d8        -       0x00000028   Zero   RW         4225    .bss                app_oled.o
    0x20000a00        -       0x000000a8   Zero   RW         4347    .bss                app_pid.o
    0x20000aa8        -       0x00000258   Zero   RW         4482    .bss                app_trajectory.o
    0x20000d00        -       0x00000080   Zero   RW         4681    .bss                app_uasrt.o
    0x20000d80        -       0x00000080   Zero   RW         4682    .bss                app_uasrt.o
    0x20000e00        -       0x00000080   Zero   RW         4683    .bss                app_uasrt.o
    0x20000e80        -       0x00000080   Zero   RW         4684    .bss                app_uasrt.o
    0x20000f00        -       0x00000080   Zero   RW         4685    .bss                app_uasrt.o
    0x20000f80        -       0x000001a4   Zero   RW         4686    .bss                app_uasrt.o
    0x20001124        -       0x00000080   Zero   RW         4687    .bss                app_uasrt.o
    0x200011a4        -       0x00000080   Zero   RW         4688    .bss                app_uasrt.o
    0x20001224        -       0x0000000c   Zero   RW         4689    .bss                app_uasrt.o
    0x20001230        -       0x0000000c   Zero   RW         4690    .bss                app_uasrt.o
    0x2000123c   0x0800af18   0x00000004   PAD
    0x20001240        -       0x00000050   Zero   RW         4815    .bss                app_laser_draw.o
    0x20001290        -       0x00000060   Zero   RW         5212    .bss                c_w.l(libspace.o)
    0x200012f0        -       0x00000800   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20001af0        -       0x00000f00   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800af18, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        52          8          0          4          0       6649   app_botton.o
       708        298        112          0          0       3216   app_hmi.o
       924         62          0         12         80       8631   app_laser_draw.o
      1724        718          0          4          0       5196   app_maixcam.o
       292         36          0          0          0       2841   app_motor.o
         0          0          0          0         40       1379   app_oled.o
      1468        228          0         92        168       9573   app_pid.o
      5692       2544         76          9        600      22216   app_trajectory.o
      2858       1304        135         40       1340      12400   app_uasrt.o
       124          4          0          0          0        878   dma.o
       958         60          0          0          0       7087   emm_v5.o
       248         18          0          0          0       1103   gpio.o
       300         46          0          0        168       2458   i2c.o
       564        116          0          1        240     708143   main.o
       210         18          0          8          0       4653   multitimer.o
       246         12          0          0          0       5192   pid.o
       364          0          0          0          0       6347   ringbuffer.o
        64         26        392          0       5888        856   startup_stm32f407xx.o
       144         24          0         12          0       8893   stm32f4xx_hal.o
       198         14          0          0          0      33927   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7510   stm32f4xx_hal_dma.o
       506         46          0          0          0       2248   stm32f4xx_hal_gpio.o
       392         16          0          0          0       3259   stm32f4xx_hal_i2c.o
        48          6          0          0          0        894   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5368   stm32f4xx_hal_rcc.o
      1470         80          0          0          0      12201   stm32f4xx_hal_tim.o
       228         28          0          0          0       2252   stm32f4xx_hal_tim_ex.o
      2076         28          0          0          0      15281   stm32f4xx_hal_uart.o
       300         90          0          0          0       9422   stm32f4xx_it.o
        16          4         24          4          0       1183   system_stm32f4xx.o
      1248        134          0          0        432       7519   tim.o
      1532        242          0          0       1480       5990   usart.o

    ----------------------------------------------------------------------
     27424       <USER>        <GROUP>        192      10440     924765   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        42          0          3          6          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
       224          0          0          0          0         96   _scanf_str.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
        26          0          0          0          0         80   atoi.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        32          0          0          0          0         80   strcspn.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        22          0          0          0          0         68   strrchr.o
        28          0          0          0          0         80   strspn.o
        12          6          0          4          0         68   strtok.o
        68          4          0          0          0         84   strtok_int.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        84          0          0          0          0        232   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
        84         16          0          0          0        372   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      1080        142        200          0          0        188   rred.o
       200         20          0          0          0        164   sin.o
       304         24         40          0          0        208   sin_i.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     15578        <USER>        <GROUP>          4        100      11120   Library Totals
        36          0          5          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      9880        334        551          4         96       6548   c_w.l
      2984        244          0          0          0       2728   fz_wm.l
      2678        268        288          0          0       1844   m_wm.l

    ----------------------------------------------------------------------
     15578        <USER>        <GROUP>          4        100      11120   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     43002       7144       1626        196      10540     909189   Grand Totals
     43002       7144       1626        196      10540     909189   ELF Image Totals
     43002       7144       1626        196          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                44628 (  43.58kB)
    Total RW  Size (RW Data + ZI Data)             10736 (  10.48kB)
    Total ROM Size (Code + RO Data + RW Data)      44824 (  43.77kB)

==============================================================================

